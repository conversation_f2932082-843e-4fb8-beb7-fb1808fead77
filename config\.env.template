# API Keys Configuration Template
# Copy this file to api_keys.env and fill in your actual API keys

# OpenAI API Key (required for GPT models)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (optional, for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Financial Data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
QUANDL_API_KEY=your_quandl_key_here
POLYGON_API_KEY=your_polygon_key_here
FINNHUB_API_KEY=your_finnhub_key_here

# News APIs
NEWS_API_KEY=your_news_api_key_here
BLOOMBERG_API_KEY=your_bloomberg_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///intraday_analyzer.db
REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/intraday_analyzer.log

# Trading Configuration
DEFAULT_SYMBOLS=RELIANCE,TATASTEEL,HDFCBANK,ICICIBANK,INFY
DEFAULT_INTERVAL=15m
DEFAULT_PERIOD=1d

# Risk Management
MAX_POSITION_SIZE=100000
MAX_DAILY_LOSS=5000
STOP_LOSS_PERCENTAGE=2.0
TAKE_PROFIT_PERCENTAGE=3.0

# Notification Settings
ENABLE_EMAIL_ALERTS=false
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

ENABLE_SLACK_ALERTS=false
SLACK_WEBHOOK_URL=your_slack_webhook_url

ENABLE_TELEGRAM_ALERTS=false
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# CrewAI Configuration
CREWAI_VERBOSE=2
CREWAI_MEMORY=true
CREWAI_MAX_ITER=5
CREWAI_MAX_EXECUTION_TIME=300

# Strategy Configuration
ENABLE_TRENDLINE_BREAKOUT=true
ENABLE_FISHER_TRANSFORM=true
ENABLE_SECTOR_ROTATION=true
ENABLE_GAP_FILL=true
ENABLE_MA_RIBBON=true
ENABLE_OPTION_CHAIN=true
ENABLE_GLOBAL_CORRELATION=true
ENABLE_VWAP=true

# Backtesting Configuration
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_CASH=100000
BACKTEST_COMMISSION=0.001

# Performance Monitoring
ENABLE_PERFORMANCE_TRACKING=true
PERFORMANCE_DB_URL=sqlite:///performance.db

# Development Settings
DEBUG=false
TESTING=false
MOCK_DATA=false
