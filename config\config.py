import os
from typing import List, Dict, Any
from dotenv import load_dotenv

class Config:
    """Configuration manager for the Intraday Trading Analyzer"""
    
    def __init__(self, env_file: str = "config/api_keys.env"):
        """Initialize configuration by loading environment variables"""
        load_dotenv(env_file)
        self._validate_required_keys()
    
    def _validate_required_keys(self):
        """Validate that required API keys are present"""
        required_keys = ['OPENAI_API_KEY']
        missing_keys = []
        
        for key in required_keys:
            if not os.getenv(key) or os.getenv(key) == 'your_openai_api_key_here':
                missing_keys.append(key)
        
        if missing_keys:
            print(f"Warning: Missing required API keys: {', '.join(missing_keys)}")
            print("Please update config/api_keys.env with your actual API keys")
    
    # API Keys
    @property
    def openai_api_key(self) -> str:
        return os.getenv('OPENAI_API_KEY', '')
    
    @property
    def anthropic_api_key(self) -> str:
        return os.getenv('ANTHROPIC_API_KEY', '')
    
    @property
    def alpha_vantage_api_key(self) -> str:
        return os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
    
    @property
    def news_api_key(self) -> str:
        return os.getenv('NEWS_API_KEY', 'demo')
    
    # Database Configuration
    @property
    def database_url(self) -> str:
        return os.getenv('DATABASE_URL', 'sqlite:///intraday_analyzer.db')
    
    @property
    def redis_url(self) -> str:
        return os.getenv('REDIS_URL', 'redis://localhost:6379')
    
    # Logging Configuration
    @property
    def log_level(self) -> str:
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def log_file(self) -> str:
        return os.getenv('LOG_FILE', 'logs/intraday_analyzer.log')
    
    # Trading Configuration
    @property
    def default_symbols(self) -> List[str]:
        symbols_str = os.getenv('DEFAULT_SYMBOLS', 'RELIANCE,TATASTEEL,HDFCBANK,ICICIBANK,INFY')
        return [s.strip() for s in symbols_str.split(',')]
    
    @property
    def default_interval(self) -> str:
        return os.getenv('DEFAULT_INTERVAL', '15m')
    
    @property
    def default_period(self) -> str:
        return os.getenv('DEFAULT_PERIOD', '1d')
    
    # Risk Management
    @property
    def max_position_size(self) -> float:
        return float(os.getenv('MAX_POSITION_SIZE', '100000'))
    
    @property
    def max_daily_loss(self) -> float:
        return float(os.getenv('MAX_DAILY_LOSS', '5000'))
    
    @property
    def stop_loss_percentage(self) -> float:
        return float(os.getenv('STOP_LOSS_PERCENTAGE', '2.0'))
    
    @property
    def take_profit_percentage(self) -> float:
        return float(os.getenv('TAKE_PROFIT_PERCENTAGE', '3.0'))
    
    # CrewAI Configuration
    @property
    def crewai_verbose(self) -> int:
        return int(os.getenv('CREWAI_VERBOSE', '2'))
    
    @property
    def crewai_memory(self) -> bool:
        return os.getenv('CREWAI_MEMORY', 'true').lower() == 'true'
    
    @property
    def crewai_max_iter(self) -> int:
        return int(os.getenv('CREWAI_MAX_ITER', '5'))
    
    @property
    def crewai_max_execution_time(self) -> int:
        return int(os.getenv('CREWAI_MAX_EXECUTION_TIME', '300'))
    
    # Strategy Configuration
    @property
    def enabled_strategies(self) -> Dict[str, bool]:
        return {
            'trendline_breakout': os.getenv('ENABLE_TRENDLINE_BREAKOUT', 'true').lower() == 'true',
            'fisher_transform': os.getenv('ENABLE_FISHER_TRANSFORM', 'true').lower() == 'true',
            'sector_rotation': os.getenv('ENABLE_SECTOR_ROTATION', 'true').lower() == 'true',
            'gap_fill': os.getenv('ENABLE_GAP_FILL', 'true').lower() == 'true',
            'ma_ribbon': os.getenv('ENABLE_MA_RIBBON', 'true').lower() == 'true',
            'option_chain': os.getenv('ENABLE_OPTION_CHAIN', 'true').lower() == 'true',
            'global_correlation': os.getenv('ENABLE_GLOBAL_CORRELATION', 'true').lower() == 'true',
            'vwap': os.getenv('ENABLE_VWAP', 'true').lower() == 'true'
        }
    
    # Development Settings
    @property
    def debug(self) -> bool:
        return os.getenv('DEBUG', 'false').lower() == 'true'
    
    @property
    def testing(self) -> bool:
        return os.getenv('TESTING', 'false').lower() == 'true'
    
    @property
    def mock_data(self) -> bool:
        return os.getenv('MOCK_DATA', 'false').lower() == 'true'
    
    # Notification Settings
    @property
    def email_alerts_enabled(self) -> bool:
        return os.getenv('ENABLE_EMAIL_ALERTS', 'false').lower() == 'true'
    
    @property
    def slack_alerts_enabled(self) -> bool:
        return os.getenv('ENABLE_SLACK_ALERTS', 'false').lower() == 'true'
    
    @property
    def telegram_alerts_enabled(self) -> bool:
        return os.getenv('ENABLE_TELEGRAM_ALERTS', 'false').lower() == 'true'
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration as a dictionary"""
        return {
            'api_keys': {
                'openai': bool(self.openai_api_key),
                'anthropic': bool(self.anthropic_api_key),
                'alpha_vantage': self.alpha_vantage_api_key,
                'news_api': self.news_api_key
            },
            'trading': {
                'default_symbols': self.default_symbols,
                'default_interval': self.default_interval,
                'default_period': self.default_period
            },
            'risk_management': {
                'max_position_size': self.max_position_size,
                'max_daily_loss': self.max_daily_loss,
                'stop_loss_percentage': self.stop_loss_percentage,
                'take_profit_percentage': self.take_profit_percentage
            },
            'strategies': self.enabled_strategies,
            'development': {
                'debug': self.debug,
                'testing': self.testing,
                'mock_data': self.mock_data
            }
        }


# Global configuration instance
config = Config()
