import re
from typing import Dict, List

# Try to import NLTK components, but continue without them if not available
try:
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    print("Warning: NLTK not fully available. Using simplified NLP processing.")

class NLPProcessor:
    def __init__(self):
        # Download required NLTK data (with error handling)
        self.sia = None
        self.stop_words = set()

        if NLTK_AVAILABLE:
            try:
                nltk.download('vader_lexicon', quiet=True)
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                self.sia = SentimentIntensityAnalyzer()
                self.stop_words = set(stopwords.words('english'))
            except Exception as e:
                print(f"NLTK setup error: {e}")
                self.sia = None
                self.stop_words = set()

        # Fallback stop words if NLTK not available
        if not self.stop_words:
            self.stop_words = {
                'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
                'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
                'to', 'was', 'will', 'with', 'the', 'this', 'but', 'they', 'have',
                'had', 'what', 'said', 'each', 'which', 'their', 'time', 'if'
            }
        
        # Indian stock symbols and company names mapping
        self.stock_mapping = {
            'RELIANCE': ['reliance', 'ril', 'reliance industries'],
            'TATASTEEL': ['tata steel', 'tatasteel', 'tata', 'steel'],
            'HDFCBANK': ['hdfc bank', 'hdfcbank', 'hdfc'],
            'ICICIBANK': ['icici bank', 'icicibank', 'icici'],
            'INFY': ['infosys', 'infy', 'info'],
            'TCS': ['tcs', 'tata consultancy', 'tata consultancy services'],
            'WIPRO': ['wipro'],
            'HCLTECH': ['hcl tech', 'hcltech', 'hcl technologies'],
            'BAJFINANCE': ['bajaj finance', 'bajfinance', 'bajaj'],
            'MARUTI': ['maruti', 'maruti suzuki'],
            'ASIANPAINT': ['asian paints', 'asianpaint'],
            'NESTLEIND': ['nestle', 'nestleind', 'nestle india'],
            'KOTAKBANK': ['kotak', 'kotak bank', 'kotakbank'],
            'LT': ['larsen toubro', 'l&t', 'lt'],
            'BHARTIARTL': ['bharti airtel', 'bhartiartl', 'airtel'],
            'ITC': ['itc'],
            'HINDUNILVR': ['hindustan unilever', 'hindunilvr', 'hul'],
            'SBIN': ['sbi', 'state bank', 'sbin'],
            'AXISBANK': ['axis bank', 'axisbank', 'axis'],
            'TECHM': ['tech mahindra', 'techm']
        }
        
        # Positive and negative sentiment keywords
        self.positive_keywords = [
            'bullish', 'rally', 'surge', 'gain', 'rise', 'up', 'positive', 'strong',
            'outperform', 'beat', 'exceed', 'growth', 'profit', 'revenue', 'upgrade',
            'buy', 'recommend', 'target', 'optimistic', 'robust', 'solid', 'good'
        ]
        
        self.negative_keywords = [
            'bearish', 'fall', 'drop', 'decline', 'down', 'negative', 'weak',
            'underperform', 'miss', 'loss', 'deficit', 'downgrade', 'sell',
            'concern', 'worry', 'risk', 'pressure', 'challenge', 'poor', 'bad'
        ]
    
    def analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of given text"""
        if not text:
            return {'sentiment': 'neutral', 'score': 0, 'stocks': []}
        
        text_lower = text.lower()
        
        # Extract mentioned stocks
        mentioned_stocks = self._extract_stocks(text_lower)
        
        # Calculate sentiment score
        sentiment_score = self._calculate_sentiment_score(text_lower)
        
        # Determine sentiment category
        if sentiment_score > 0.1:
            sentiment = 'positive'
        elif sentiment_score < -0.1:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'score': sentiment_score,
            'stocks': mentioned_stocks
        }
    
    def _extract_stocks(self, text: str) -> List[str]:
        """Extract stock symbols mentioned in text"""
        mentioned_stocks = []
        
        for symbol, keywords in self.stock_mapping.items():
            for keyword in keywords:
                if keyword in text:
                    if symbol not in mentioned_stocks:
                        mentioned_stocks.append(symbol)
                    break
        
        return mentioned_stocks
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score using multiple methods"""
        scores = []
        
        # Method 1: VADER sentiment (if available)
        if self.sia:
            try:
                vader_score = self.sia.polarity_scores(text)['compound']
                scores.append(vader_score)
            except:
                pass
        
        # Method 2: Keyword-based sentiment
        keyword_score = self._keyword_sentiment_score(text)
        scores.append(keyword_score)
        
        # Method 3: Pattern-based sentiment
        pattern_score = self._pattern_sentiment_score(text)
        scores.append(pattern_score)
        
        # Return average of available scores
        if scores:
            return sum(scores) / len(scores)
        else:
            return 0.0
    
    def _keyword_sentiment_score(self, text: str) -> float:
        """Calculate sentiment based on positive/negative keywords"""
        if NLTK_AVAILABLE:
            try:
                words = word_tokenize(text.lower()) if text else []
            except:
                words = text.lower().split() if text else []
        else:
            words = text.lower().split() if text else []

        positive_count = sum(1 for word in words if word in self.positive_keywords)
        negative_count = sum(1 for word in words if word in self.negative_keywords)

        total_words = len([w for w in words if w not in self.stop_words])

        if total_words == 0:
            return 0.0

        # Normalize by total meaningful words
        positive_ratio = positive_count / total_words
        negative_ratio = negative_count / total_words

        return positive_ratio - negative_ratio
    
    def _pattern_sentiment_score(self, text: str) -> float:
        """Calculate sentiment based on common patterns"""
        score = 0.0
        
        # Positive patterns
        positive_patterns = [
            r'(beats?|exceed[s]?|outperform[s]?) (estimate[s]?|expectation[s]?)',
            r'(strong|robust|solid) (growth|performance|result[s]?)',
            r'(upgrade[d]?|raise[d]?) (target|rating)',
            r'(buy|accumulate) (rating|recommendation)',
            r'(surge[s]?|rally|gain[s]?) (\d+%|\d+ percent)',
        ]
        
        # Negative patterns
        negative_patterns = [
            r'(miss|below|under) (estimate[s]?|expectation[s]?)',
            r'(weak|poor|disappointing) (growth|performance|result[s]?)',
            r'(downgrade[d]?|cut|lower) (target|rating)',
            r'(sell|reduce) (rating|recommendation)',
            r'(fall[s]?|drop[s]?|decline[s]?) (\d+%|\d+ percent)',
        ]
        
        for pattern in positive_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                score += 0.3
        
        for pattern in negative_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                score -= 0.3
        
        return max(-1.0, min(1.0, score))  # Clamp between -1 and 1
    
    def extract_key_phrases(self, text: str, max_phrases: int = 5) -> List[str]:
        """Extract key phrases from text"""
        if not text:
            return []

        # Simple key phrase extraction based on common financial terms
        financial_terms = [
            'earnings', 'revenue', 'profit', 'loss', 'growth', 'margin',
            'guidance', 'outlook', 'forecast', 'target', 'estimate',
            'dividend', 'buyback', 'acquisition', 'merger', 'expansion',
            'ipo', 'listing', 'delisting', 'split', 'bonus'
        ]

        phrases = []
        if NLTK_AVAILABLE:
            try:
                words = word_tokenize(text.lower())
            except:
                words = text.lower().split()
        else:
            words = text.lower().split()

        for i, word in enumerate(words):
            if word in financial_terms:
                # Extract phrase around the financial term
                start = max(0, i - 2)
                end = min(len(words), i + 3)
                phrase = ' '.join(words[start:end])
                phrases.append(phrase)

        return phrases[:max_phrases]
    
    def classify_news_category(self, text: str) -> str:
        """Classify news into categories"""
        text_lower = text.lower()
        
        categories = {
            'earnings': ['earnings', 'quarterly', 'results', 'profit', 'revenue'],
            'policy': ['rbi', 'policy', 'rate', 'government', 'regulation'],
            'market': ['market', 'index', 'nifty', 'sensex', 'trading'],
            'sector': ['sector', 'industry', 'banking', 'it', 'pharma', 'auto'],
            'global': ['global', 'international', 'us', 'china', 'fed'],
            'corporate': ['merger', 'acquisition', 'ipo', 'listing', 'expansion']
        }
        
        category_scores = {}
        for category, keywords in categories.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        else:
            return 'general'
