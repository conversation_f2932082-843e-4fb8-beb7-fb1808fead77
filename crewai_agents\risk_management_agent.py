"""
Risk Management Agent for CrewAI
Specialized agent for Indian market risk management
"""

from crewai_tools import BaseTool
from typing import Dict, Any
import json

class RiskManagementAgent(BaseTool):
    name: str = "Indian Market Risk Manager"
    description: str = """
    Provides risk management and position sizing for Indian stock trades.
    Understands Indian market volatility, provides appropriate stop-losses,
    position sizing, and risk-adjusted returns.
    """
    
    def _run(self, strategy_signals: str, capital: str = "100000") -> Dict[str, Any]:
        """Provide comprehensive risk management for trading signals"""
        
        try:
            print(f"🛡️ Calculating risk management parameters...")
            
            # Parse inputs
            if isinstance(strategy_signals, str):
                try:
                    signals = json.loads(strategy_signals)
                except:
                    signals = {}
            else:
                signals = strategy_signals
            
            total_capital = float(capital)
            
            risk_analysis = {}
            portfolio_risk = {
                'total_capital': total_capital,
                'max_risk_per_trade': total_capital * 0.02,  # 2% max risk per trade
                'max_portfolio_risk': total_capital * 0.06,  # 6% max portfolio risk
                'position_size_per_trade': total_capital * 0.20,  # 20% per trade
                'max_concurrent_positions': 3
            }
            
            # Analyze each signal
            strategy_results = signals.get('strategy_results', {})
            
            for symbol, strategies in strategy_results.items():
                print(f"   📊 Risk analysis for {symbol}...")
                
                # Get best strategy for the stock
                best_strategy = self._get_best_strategy(strategies)
                
                if best_strategy and best_strategy['signal'] in ['BUY', 'SELL']:
                    risk_params = self._calculate_risk_parameters(
                        symbol, best_strategy, portfolio_risk
                    )
                    risk_analysis[symbol] = risk_params
                    
                    # Display key risk metrics
                    risk_reward = risk_params.get('risk_reward_ratio', 0)
                    max_loss = risk_params.get('max_loss_amount', 0)
                    print(f"      🎯 R:R Ratio: {risk_reward:.2f} | Max Loss: ₹{max_loss:,.0f}")
            
            # Calculate portfolio-level risk
            portfolio_metrics = self._calculate_portfolio_risk(risk_analysis, portfolio_risk)
            
            risk_summary = {
                'individual_risks': risk_analysis,
                'portfolio_risk': portfolio_metrics,
                'risk_recommendations': self._generate_risk_recommendations(risk_analysis, portfolio_metrics),
                'position_sizing': self._optimize_position_sizing(risk_analysis, total_capital),
                'risk_alerts': self._generate_risk_alerts(risk_analysis, portfolio_metrics)
            }
            
            print(f"✅ Risk analysis completed for {len(risk_analysis)} positions")
            
            return risk_summary
            
        except Exception as e:
            print(f"❌ Error in risk management: {e}")
            return {
                'error': str(e),
                'individual_risks': {},
                'portfolio_risk': {},
                'risk_recommendations': [],
                'position_sizing': {},
                'risk_alerts': []
            }
    
    def _get_best_strategy(self, strategies: Dict) -> Dict[str, Any]:
        """Get best strategy from multiple strategies"""
        
        valid_strategies = [s for s in strategies.values() if 'error' not in s and s.get('score', 0) > 0]
        
        if not valid_strategies:
            return None
        
        return max(valid_strategies, key=lambda x: x.get('score', 0))
    
    def _calculate_risk_parameters(self, symbol: str, strategy: Dict, portfolio_risk: Dict) -> Dict[str, Any]:
        """Calculate risk parameters for a single position"""
        
        try:
            entry_price = strategy.get('entry_price', 0)
            stop_loss = strategy.get('stop_loss')
            target = strategy.get('target')
            signal = strategy.get('signal')
            confidence = strategy.get('confidence', 'Low')
            
            if not entry_price or not stop_loss:
                return {'error': 'Missing price data'}
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            reward_per_share = abs(target - entry_price) if target else risk_per_share * 2
            
            # Risk-reward ratio
            risk_reward_ratio = reward_per_share / risk_per_share if risk_per_share > 0 else 0
            
            # Position sizing based on risk
            max_risk_amount = portfolio_risk['max_risk_per_trade']
            max_shares = int(max_risk_amount / risk_per_share) if risk_per_share > 0 else 0
            
            # Position size based on capital allocation
            position_value = portfolio_risk['position_size_per_trade']
            shares_by_capital = int(position_value / entry_price) if entry_price > 0 else 0
            
            # Take minimum for conservative approach
            recommended_shares = min(max_shares, shares_by_capital)
            actual_position_value = recommended_shares * entry_price
            actual_risk_amount = recommended_shares * risk_per_share
            
            # Confidence-based adjustments
            confidence_multiplier = {
                'High': 1.0,
                'Medium': 0.75,
                'Low': 0.5
            }.get(confidence, 0.5)
            
            adjusted_shares = int(recommended_shares * confidence_multiplier)
            adjusted_position_value = adjusted_shares * entry_price
            adjusted_risk_amount = adjusted_shares * risk_per_share
            
            # Indian market specific adjustments
            indian_volatility_factor = self._get_indian_volatility_factor(symbol)
            
            return {
                'symbol': symbol,
                'signal': signal,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'target': target,
                'risk_per_share': risk_per_share,
                'reward_per_share': reward_per_share,
                'risk_reward_ratio': risk_reward_ratio,
                'max_shares': max_shares,
                'recommended_shares': adjusted_shares,
                'position_value': adjusted_position_value,
                'max_loss_amount': adjusted_risk_amount,
                'max_profit_potential': adjusted_shares * reward_per_share,
                'confidence': confidence,
                'confidence_multiplier': confidence_multiplier,
                'indian_volatility_factor': indian_volatility_factor,
                'risk_grade': self._assign_risk_grade(risk_reward_ratio, confidence, indian_volatility_factor)
            }
            
        except Exception as e:
            return {'error': str(e), 'symbol': symbol}
    
    def _get_indian_volatility_factor(self, symbol: str) -> float:
        """Get volatility factor for Indian stocks"""
        
        # Indian stock volatility factors (based on historical data)
        volatility_factors = {
            'RELIANCE': 0.8,    # Lower volatility, large cap
            'TCS': 0.7,         # Very stable, IT major
            'HDFCBANK': 0.9,    # Banking sector volatility
            'ICICIBANK': 1.0,   # Higher banking volatility
            'INFY': 0.8,        # Stable IT stock
            'HINDUNILVR': 0.6,  # FMCG stability
            'ITC': 0.7,         # Stable FMCG
            'SBIN': 1.2,        # PSU bank volatility
            'BHARTIARTL': 1.0,  # Telecom volatility
            'KOTAKBANK': 0.9    # Private bank
        }
        
        return volatility_factors.get(symbol, 1.0)  # Default to 1.0
    
    def _assign_risk_grade(self, risk_reward: float, confidence: str, volatility: float) -> str:
        """Assign risk grade to the trade"""
        
        # Calculate composite risk score
        rr_score = min(risk_reward / 2.0, 1.0)  # Normalize to 1.0
        conf_score = {'High': 1.0, 'Medium': 0.7, 'Low': 0.4}.get(confidence, 0.4)
        vol_score = max(0, 1.0 - (volatility - 0.5))  # Lower volatility = higher score
        
        composite_score = (rr_score + conf_score + vol_score) / 3
        
        if composite_score >= 0.8:
            return 'A'  # Excellent
        elif composite_score >= 0.6:
            return 'B'  # Good
        elif composite_score >= 0.4:
            return 'C'  # Average
        else:
            return 'D'  # Poor
    
    def _calculate_portfolio_risk(self, individual_risks: Dict, portfolio_config: Dict) -> Dict[str, Any]:
        """Calculate portfolio-level risk metrics"""
        
        try:
            total_position_value = sum(r.get('position_value', 0) for r in individual_risks.values() if 'error' not in r)
            total_risk_amount = sum(r.get('max_loss_amount', 0) for r in individual_risks.values() if 'error' not in r)
            total_profit_potential = sum(r.get('max_profit_potential', 0) for r in individual_risks.values() if 'error' not in r)
            
            portfolio_risk_percentage = (total_risk_amount / portfolio_config['total_capital']) * 100
            portfolio_utilization = (total_position_value / portfolio_config['total_capital']) * 100
            
            # Risk distribution
            risk_grades = [r.get('risk_grade', 'D') for r in individual_risks.values() if 'error' not in r]
            grade_distribution = {
                'A': risk_grades.count('A'),
                'B': risk_grades.count('B'),
                'C': risk_grades.count('C'),
                'D': risk_grades.count('D')
            }
            
            return {
                'total_positions': len(individual_risks),
                'total_position_value': total_position_value,
                'total_risk_amount': total_risk_amount,
                'total_profit_potential': total_profit_potential,
                'portfolio_risk_percentage': portfolio_risk_percentage,
                'portfolio_utilization': portfolio_utilization,
                'average_risk_reward': total_profit_potential / total_risk_amount if total_risk_amount > 0 else 0,
                'risk_grade_distribution': grade_distribution,
                'within_risk_limits': portfolio_risk_percentage <= 6.0,  # 6% max portfolio risk
                'diversification_score': len(individual_risks) / portfolio_config['max_concurrent_positions']
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _generate_risk_recommendations(self, individual_risks: Dict, portfolio_metrics: Dict) -> list:
        """Generate risk management recommendations"""
        
        recommendations = []
        
        # Portfolio level recommendations
        if portfolio_metrics.get('portfolio_risk_percentage', 0) > 6:
            recommendations.append({
                'type': 'PORTFOLIO_RISK',
                'priority': 'HIGH',
                'message': f"Portfolio risk ({portfolio_metrics['portfolio_risk_percentage']:.1f}%) exceeds 6% limit",
                'action': 'Reduce position sizes or number of positions'
            })
        
        if portfolio_metrics.get('portfolio_utilization', 0) > 80:
            recommendations.append({
                'type': 'CAPITAL_UTILIZATION',
                'priority': 'MEDIUM',
                'message': f"High capital utilization ({portfolio_metrics['portfolio_utilization']:.1f}%)",
                'action': 'Consider keeping some cash for opportunities'
            })
        
        # Individual position recommendations
        for symbol, risk_data in individual_risks.items():
            if 'error' in risk_data:
                continue
                
            risk_grade = risk_data.get('risk_grade', 'D')
            risk_reward = risk_data.get('risk_reward_ratio', 0)
            
            if risk_grade == 'D':
                recommendations.append({
                    'type': 'POOR_RISK_REWARD',
                    'priority': 'HIGH',
                    'symbol': symbol,
                    'message': f"{symbol} has poor risk grade (D)",
                    'action': 'Consider avoiding this trade or reducing position size'
                })
            
            if risk_reward < 1.5:
                recommendations.append({
                    'type': 'LOW_RISK_REWARD',
                    'priority': 'MEDIUM',
                    'symbol': symbol,
                    'message': f"{symbol} has low risk-reward ratio ({risk_reward:.2f})",
                    'action': 'Look for better risk-reward opportunities'
                })
        
        return recommendations
    
    def _optimize_position_sizing(self, individual_risks: Dict, total_capital: float) -> Dict[str, Any]:
        """Optimize position sizing across all trades"""
        
        optimization = {}
        
        # Sort by risk grade and risk-reward ratio
        sorted_positions = sorted(
            [(symbol, data) for symbol, data in individual_risks.items() if 'error' not in data],
            key=lambda x: (x[1].get('risk_grade', 'D'), x[1].get('risk_reward_ratio', 0)),
            reverse=True
        )
        
        allocated_capital = 0
        allocated_risk = 0
        max_portfolio_risk = total_capital * 0.06  # 6%
        
        for symbol, risk_data in sorted_positions:
            if allocated_risk + risk_data.get('max_loss_amount', 0) <= max_portfolio_risk:
                optimization[symbol] = {
                    'recommended': True,
                    'position_size': risk_data.get('position_value', 0),
                    'shares': risk_data.get('recommended_shares', 0),
                    'risk_amount': risk_data.get('max_loss_amount', 0),
                    'priority': len(optimization) + 1
                }
                allocated_capital += risk_data.get('position_value', 0)
                allocated_risk += risk_data.get('max_loss_amount', 0)
            else:
                optimization[symbol] = {
                    'recommended': False,
                    'reason': 'Exceeds portfolio risk limit',
                    'alternative_size': 0
                }
        
        optimization['summary'] = {
            'total_allocated_capital': allocated_capital,
            'total_allocated_risk': allocated_risk,
            'capital_utilization': (allocated_capital / total_capital) * 100,
            'risk_utilization': (allocated_risk / max_portfolio_risk) * 100,
            'recommended_positions': len([p for p in optimization.values() if isinstance(p, dict) and p.get('recommended')])
        }
        
        return optimization
    
    def _generate_risk_alerts(self, individual_risks: Dict, portfolio_metrics: Dict) -> list:
        """Generate risk alerts"""
        
        alerts = []
        
        # High risk alerts
        for symbol, risk_data in individual_risks.items():
            if 'error' in risk_data:
                continue
                
            if risk_data.get('risk_reward_ratio', 0) < 1.0:
                alerts.append({
                    'level': 'HIGH',
                    'symbol': symbol,
                    'message': f"Risk-reward ratio below 1.0 ({risk_data['risk_reward_ratio']:.2f})",
                    'recommendation': 'Avoid this trade'
                })
            
            if risk_data.get('indian_volatility_factor', 1.0) > 1.2:
                alerts.append({
                    'level': 'MEDIUM',
                    'symbol': symbol,
                    'message': f"High volatility stock (factor: {risk_data['indian_volatility_factor']:.1f})",
                    'recommendation': 'Use smaller position size'
                })
        
        # Portfolio alerts
        if portfolio_metrics.get('portfolio_risk_percentage', 0) > 5:
            alerts.append({
                'level': 'HIGH',
                'message': f"Portfolio risk approaching limit ({portfolio_metrics['portfolio_risk_percentage']:.1f}%)",
                'recommendation': 'Monitor risk levels closely'
            })
        
        return alerts
