
ERROR GENERATING AGENTIC ANALYSIS REPORT
========================================

Error: Invalid format specifier '.2f if stop_loss else 0:.2f' for object of type 'float'

Falling back to basic analysis...

BASIC STOCK ANALYSIS
===================

Stocks being analyzed: ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY']
Total stocks: 5

The system is configured to analyze 5 major Indian stocks:
1. RELIANCE - Reliance Industries Limited
2. TCS - Tata Consultancy Services
3. HDFCBANK - HDFC Bank Limited
4. ICICIBANK - ICICI Bank Limited
5. INFY - Infosys Limited

These stocks represent major sectors:
- Energy & Petrochemicals (RELIANCE)
- Information Technology (TCS, INFY)
- Banking & Financial Services (HDFCBANK, ICICIBANK)

Analysis includes:
- Real-time price data from NSE/BSE
- Technical indicators (RSI, MACD, <PERSON><PERSON><PERSON> Bands)
- News sentiment analysis
- Trading signals and recommendations
- Risk management parameters

For detailed analysis, please run:
python smart_market_analyzer.py (fallback system)
