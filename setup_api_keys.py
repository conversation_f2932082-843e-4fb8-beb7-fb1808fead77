#!/usr/bin/env python3
"""
<PERSON><PERSON>t to help set up free API keys for financial data sources
This script will guide you through getting free API keys from various providers
"""

import webbrowser
import time
import os
from pathlib import Path

class APIKeySetup:
    def __init__(self):
        self.api_providers = {
            'Alpha Vantage': {
                'url': 'https://www.alphavantage.co/support/#api-key',
                'description': 'Free stock market data API',
                'free_tier': '25 requests per day',
                'features': 'Real-time quotes, historical data, technical indicators',
                'key_format': 'ALPHA_VANTAGE_API_KEY=YOUR_KEY_HERE'
            },
            'Finnhub': {
                'url': 'https://finnhub.io/register',
                'description': 'Financial data and news API',
                'free_tier': '60 calls per minute',
                'features': 'Stock quotes, company news, earnings data',
                'key_format': 'FINNHUB_API_KEY=YOUR_KEY_HERE'
            },
            'News API': {
                'url': 'https://newsapi.org/register',
                'description': 'News articles from various sources',
                'free_tier': '1000 requests per day',
                'features': 'Financial news, market sentiment analysis',
                'key_format': 'NEWS_API_KEY=YOUR_KEY_HERE'
            },
            'Polygon': {
                'url': 'https://polygon.io/pricing',
                'description': 'Real-time and historical market data',
                'free_tier': '5 calls per minute',
                'features': 'Stock data, options, forex, crypto',
                'key_format': 'POLYGON_API_KEY=YOUR_KEY_HERE'
            }
        }
    
    def display_welcome(self):
        print("🔑 API KEY SETUP WIZARD")
        print("=" * 50)
        print("This wizard will help you set up FREE API keys for enhanced market data.")
        print("All these APIs offer free tiers that are perfect for learning and development.")
        print("\n📊 Current Status:")
        print("✅ Yahoo Finance: Already working (no API key needed)")
        print("✅ MoneyControl: Already working (web scraping)")
        print("🔧 Enhanced APIs: Need free registration")
        print("\n" + "=" * 50)
    
    def setup_provider(self, provider_name, provider_info):
        print(f"\n🌟 Setting up {provider_name}")
        print("-" * 40)
        print(f"📝 Description: {provider_info['description']}")
        print(f"🆓 Free Tier: {provider_info['free_tier']}")
        print(f"⚡ Features: {provider_info['features']}")
        print(f"🔗 Registration URL: {provider_info['url']}")
        
        choice = input(f"\n🚀 Open {provider_name} registration page? (y/n): ").lower().strip()
        
        if choice == 'y':
            print(f"🌐 Opening {provider_name} registration page...")
            webbrowser.open(provider_info['url'])
            
            print(f"\n📋 Steps to get your {provider_name} API key:")
            if provider_name == 'Alpha Vantage':
                print("1. Fill in your email address")
                print("2. Click 'GET FREE API KEY'")
                print("3. Check your email for the API key")
                print("4. Copy the API key (format: XXXXXXXXXXXXXXXX)")
            
            elif provider_name == 'Finnhub':
                print("1. Sign up with email and password")
                print("2. Verify your email address")
                print("3. Go to Dashboard -> API Keys")
                print("4. Copy your API key")
            
            elif provider_name == 'News API':
                print("1. Fill in your details and register")
                print("2. Verify your email address")
                print("3. Go to your account dashboard")
                print("4. Copy your API key")
            
            elif provider_name == 'Polygon':
                print("1. Sign up for free account")
                print("2. Verify your email")
                print("3. Go to Dashboard -> API Keys")
                print("4. Copy your API key")
            
            api_key = input(f"\n🔑 Enter your {provider_name} API key (or press Enter to skip): ").strip()
            
            if api_key:
                self.update_env_file(provider_info['key_format'].replace('YOUR_KEY_HERE', api_key))
                print(f"✅ {provider_name} API key saved!")
                return True
            else:
                print(f"⏭️ Skipping {provider_name} for now")
                return False
        else:
            print(f"⏭️ Skipping {provider_name}")
            return False
    
    def update_env_file(self, key_line):
        """Update the API keys environment file"""
        env_file = Path("config/api_keys.env")
        
        if not env_file.exists():
            print("❌ config/api_keys.env file not found!")
            return
        
        # Read current content
        with open(env_file, 'r') as f:
            lines = f.readlines()
        
        # Update the specific key
        key_name = key_line.split('=')[0]
        updated = False
        
        for i, line in enumerate(lines):
            if line.startswith(f"{key_name}="):
                lines[i] = key_line + '\n'
                updated = True
                break
        
        if not updated:
            lines.append(key_line + '\n')
        
        # Write back
        with open(env_file, 'w') as f:
            f.writelines(lines)
    
    def test_api_keys(self):
        """Test the configured API keys"""
        print("\n🧪 Testing API Keys...")
        print("-" * 30)
        
        try:
            from tools.enhanced_market_data import EnhancedMarketData
            
            emd = EnhancedMarketData()
            
            # Test with a sample stock
            print("📊 Testing with RELIANCE...")
            quote_data = emd.get_real_time_quote('RELIANCE')
            
            if quote_data:
                print("✅ API integration working!")
                for source, data in quote_data.items():
                    print(f"   {source}: ₹{data.get('price', 'N/A')}")
            else:
                print("⚠️ Using fallback data sources (Yahoo Finance)")
            
            # Test market status
            status = emd.get_market_status()
            print(f"🏛️ Market Status: {'Open' if status['is_open'] else 'Closed'}")
            
        except Exception as e:
            print(f"❌ Error testing APIs: {e}")
    
    def run_setup(self):
        """Run the complete setup process"""
        self.display_welcome()
        
        setup_count = 0
        
        for provider_name, provider_info in self.api_providers.items():
            if self.setup_provider(provider_name, provider_info):
                setup_count += 1
                time.sleep(1)  # Brief pause between setups
        
        print(f"\n🎉 Setup Complete!")
        print(f"✅ Configured {setup_count} API providers")
        print(f"📊 Yahoo Finance: Always available (no key needed)")
        
        if setup_count > 0:
            print("\n🧪 Testing your API configuration...")
            self.test_api_keys()
        
        print(f"\n💡 Tips:")
        print(f"• You can always add more API keys later by editing config/api_keys.env")
        print(f"• Free tiers are perfect for learning and development")
        print(f"• The system works with Yahoo Finance even without additional APIs")
        print(f"• Run 'python main_enhanced.py' to use the enhanced version")


def main():
    """Main function"""
    setup = APIKeySetup()
    setup.run_setup()


if __name__ == "__main__":
    main()
