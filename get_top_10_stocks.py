#!/usr/bin/env python3
"""
Get Top 10 Best Stocks to Trade
Runs comprehensive analysis to find the best trading opportunities
"""

import os
import sys
from datetime import datetime
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.equity_csv_stocks_fetcher import EquityCSVStocksFetcher
from tools.indian_market_data import IndianMarketData
from tools.nlp_processor import NLPProcessor

def get_top_liquid_stocks_for_analysis(count=200):
    """Get top liquid stocks for comprehensive analysis"""
    
    print("📊 GETTING TOP LIQUID STOCKS FOR ANALYSIS")
    print("=" * 50)
    
    try:
        csv_fetcher = EquityCSVStocksFetcher()
        
        # Get liquid stocks (these are most suitable for intraday trading)
        liquid_stocks = csv_fetcher.get_liquid_stocks(count)
        
        print(f"✅ Selected {len(liquid_stocks)} liquid stocks for analysis")
        print(f"📋 Sample: {', '.join(liquid_stocks[:20])}")
        
        return liquid_stocks
        
    except Exception as e:
        print(f"❌ Error getting stocks: {e}")
        # Fallback to known liquid stocks
        return [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK',
            'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH',
            'BAJFINANCE', 'POWERGRID', 'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA',
            'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJFINSV', 'BAJAJ-AUTO',
            'BRITANNIA', 'COALINDIA', 'DIVISLAB', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS'
        ]

def analyze_stock_performance(stocks):
    """Analyze stock performance and generate scores"""
    
    print(f"\n📈 ANALYZING {len(stocks)} STOCKS FOR TRADING OPPORTUNITIES")
    print("=" * 60)
    
    market_data = IndianMarketData()
    stock_scores = []
    
    for i, symbol in enumerate(stocks, 1):
        try:
            print(f"📊 Analyzing {symbol} ({i}/{len(stocks)})...")
            
            # Get stock data
            stock_quotes = market_data.get_stock_quotes([symbol])
            
            if symbol in stock_quotes and stock_quotes[symbol]:
                quote_data = stock_quotes[symbol]
                
                # Extract key metrics
                price = quote_data.get('price', 0)
                change_pct = quote_data.get('change_percent', 0)
                volume = quote_data.get('volume', 0)
                
                # Calculate trading score based on multiple factors
                score = calculate_trading_score(symbol, price, change_pct, volume)
                
                stock_scores.append({
                    'symbol': symbol,
                    'price': price,
                    'change_percent': change_pct,
                    'volume': volume,
                    'score': score,
                    'recommendation': get_recommendation(score, change_pct)
                })
                
                print(f"   ✅ {symbol}: ₹{price:.2f} ({change_pct:+.2f}%) Score: {score:.1f}")
            else:
                print(f"   ❌ {symbol}: No data available")
                
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
            continue
    
    return stock_scores

def calculate_trading_score(symbol, price, change_pct, volume):
    """Calculate trading score based on multiple factors"""
    
    score = 50  # Base score
    
    # Price momentum factor
    if abs(change_pct) > 3:
        score += 20  # High volatility
    elif abs(change_pct) > 1:
        score += 10  # Medium volatility
    
    # Volume factor (proxy)
    if volume > 1000000:
        score += 15  # High volume
    elif volume > 100000:
        score += 10  # Medium volume
    elif volume > 10000:
        score += 5   # Low volume
    
    # Price range factor
    if 50 <= price <= 5000:
        score += 10  # Good price range for trading
    elif price < 50:
        score -= 10  # Too low (penny stock risk)
    elif price > 10000:
        score -= 5   # Very high price
    
    # Trend factor
    if change_pct > 2:
        score += 15  # Strong uptrend
    elif change_pct > 0:
        score += 5   # Mild uptrend
    elif change_pct < -2:
        score += 10  # Strong downtrend (short opportunity)
    
    # Known liquid stock bonus
    liquid_stocks = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'SBIN', 'BHARTIARTL', 'ITC', 'KOTAKBANK', 'HINDUNILVR']
    if symbol in liquid_stocks:
        score += 20
    
    return min(score, 100)  # Cap at 100

def get_recommendation(score, change_pct):
    """Get trading recommendation based on score and trend"""
    
    if score >= 80:
        if change_pct > 1:
            return "STRONG BUY"
        elif change_pct < -1:
            return "STRONG SELL"
        else:
            return "BUY"
    elif score >= 70:
        if change_pct > 0:
            return "BUY"
        else:
            return "SELL"
    elif score >= 60:
        return "HOLD"
    else:
        return "AVOID"

def get_top_10_recommendations(stock_scores):
    """Get top 10 stock recommendations"""
    
    print(f"\n🎯 GENERATING TOP 10 RECOMMENDATIONS")
    print("=" * 50)
    
    # Sort by score (descending)
    sorted_stocks = sorted(stock_scores, key=lambda x: x['score'], reverse=True)
    
    # Get top 10
    top_10 = sorted_stocks[:10]
    
    print(f"\n🏆 TOP 10 BEST STOCKS TO TRADE")
    print("=" * 50)
    
    for i, stock in enumerate(top_10, 1):
        symbol = stock['symbol']
        price = stock['price']
        change_pct = stock['change_percent']
        score = stock['score']
        recommendation = stock['recommendation']
        
        trend_emoji = "🟢" if change_pct > 0 else "🔴" if change_pct < 0 else "🟡"
        
        print(f"{i:2d}. {symbol:<12} | ₹{price:>8.2f} | {change_pct:>+6.2f}% {trend_emoji} | Score: {score:>5.1f} | {recommendation}")
    
    return top_10

def generate_detailed_report(top_10):
    """Generate detailed trading report"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = f"""
🇮🇳 TOP 10 BEST INDIAN STOCKS TO TRADE
======================================
Generated: {timestamp}
Analysis: Comprehensive scoring based on price, volume, momentum, and liquidity

🏆 RANKINGS:
"""
    
    for i, stock in enumerate(top_10, 1):
        symbol = stock['symbol']
        price = stock['price']
        change_pct = stock['change_percent']
        volume = stock['volume']
        score = stock['score']
        recommendation = stock['recommendation']
        
        trend = "UP" if change_pct > 0 else "DOWN" if change_pct < 0 else "FLAT"
        
        report += f"""
{i}. {symbol} - {recommendation}
   Price: ₹{price:.2f}
   Change: {change_pct:+.2f}% ({trend})
   Volume: {volume:,}
   Trading Score: {score:.1f}/100
   
"""
    
    report += f"""
📊 ANALYSIS SUMMARY:
===================
• Total Stocks Analyzed: {len(top_10)} (from comprehensive scan)
• Best Performer: {top_10[0]['symbol']} (Score: {top_10[0]['score']:.1f})
• Strongest Trend: {max(top_10, key=lambda x: abs(x['change_percent']))['symbol']} ({max(top_10, key=lambda x: abs(x['change_percent']))['change_percent']:+.2f}%)

🎯 TRADING RECOMMENDATIONS:
==========================
• STRONG BUY: {len([s for s in top_10 if s['recommendation'] == 'STRONG BUY'])} stocks
• BUY: {len([s for s in top_10 if s['recommendation'] == 'BUY'])} stocks  
• SELL: {len([s for s in top_10 if s['recommendation'] == 'SELL'])} stocks
• STRONG SELL: {len([s for s in top_10 if s['recommendation'] == 'STRONG SELL'])} stocks

⏰ OPTIMAL TRADING TIMES:
========================
• 9:15 AM IST - Market opening (high volatility)
• 10:15 AM IST - Golden hour (best execution)
• 1:15 PM IST - Post-lunch momentum
• 2:45 PM IST - Pre-closing opportunities

🎯 NEXT STEPS:
=============
1. Focus on top 3-5 stocks for concentrated analysis
2. Set up alerts for key price levels
3. Monitor volume and momentum throughout the day
4. Use proper risk management (stop-loss, position sizing)

Report generated by Agentic AI Indian Stock Market Analyzer
🤖 Enhanced with multi-agent intelligence for optimal trading decisions
"""
    
    return report

def save_results(top_10, report):
    """Save results to files"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save JSON data
    json_filename = f"top_10_stocks_{timestamp}.json"
    with open(json_filename, 'w') as f:
        json.dump(top_10, f, indent=2)
    
    # Save text report
    txt_filename = f"top_10_stocks_report_{timestamp}.txt"
    with open(txt_filename, 'w') as f:
        f.write(report)
    
    print(f"\n💾 RESULTS SAVED:")
    print(f"   📊 Data: {json_filename}")
    print(f"   📋 Report: {txt_filename}")
    
    return json_filename, txt_filename

def main():
    """Main function to get top 10 best stocks"""
    
    print("🇮🇳 INDIAN STOCK MARKET ANALYZER")
    print("🎯 FINDING TOP 10 BEST STOCKS TO TRADE")
    print("=" * 60)
    
    try:
        # Step 1: Get liquid stocks for analysis
        stocks = get_top_liquid_stocks_for_analysis(100)  # Analyze top 100 liquid stocks
        
        # Step 2: Analyze stock performance
        stock_scores = analyze_stock_performance(stocks)
        
        if not stock_scores:
            print("❌ No stock data available for analysis")
            return
        
        # Step 3: Get top 10 recommendations
        top_10 = get_top_10_recommendations(stock_scores)
        
        # Step 4: Generate detailed report
        report = generate_detailed_report(top_10)
        
        # Step 5: Save results
        json_file, txt_file = save_results(top_10, report)
        
        # Step 6: Display summary
        print(f"\n🎉 ANALYSIS COMPLETE!")
        print(f"📊 Analyzed {len(stock_scores)} stocks")
        print(f"🏆 Top 10 recommendations generated")
        print(f"💾 Results saved to files")
        
        print(f"\n🚀 READY FOR TRADING!")
        print(f"⏰ Best times: 9:15 AM, 10:15 AM, 1:15 PM, 2:45 PM IST")
        
        return top_10
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")
        return None

if __name__ == "__main__":
    main()
