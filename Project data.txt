Here's the complete implementation for main.py and all the agents, following the architecture we designed:

1. main.py
python
import os
from dotenv import load_dotenv
from crewai import Crew
from agents.supervisor_agent import SupervisorAgent
from agents.strategy_agents.trendline_breakout_agent import TrendlineBreakoutAgent
from agents.strategy_agents.fisher_transform_agent import FisherTransformAgent
from agents.strategy_agents.sector_rotation_agent import SectorRotationAgent
from agents.strategy_agents.gap_fill_agent import GapFillAgent
from agents.strategy_agents.ma_ribbon_agent import MARibbonAgent
from agents.strategy_agents.option_chain_agent import OptionChainAgent
from agents.strategy_agents.global_correlation_agent import GlobalCorrelationAgent
from agents.strategy_agents.vwap_agent import VWAPAgent
from agents.news_analysis_agent import NewsAnalysisAgent
from agents.data_collection_agent import DataCollectionAgent
from agents.validation_agent import ValidationAgent

# Load environment variables
load_dotenv("config/api_keys.env")

class IntradayTradingCrew:
    def __init__(self):
        # Initialize all agents
        self.supervisor = SupervisorAgent().agent
        self.trendline_breakout = TrendlineBreakoutAgent().agent
        self.fisher_transform = FisherTransformAgent().agent
        self.sector_rotation = SectorRotationAgent().agent
        self.gap_fill = GapFillAgent().agent
        self.ma_ribbon = MARibbonAgent().agent
        self.option_chain = OptionChainAgent().agent
        self.global_correlation = GlobalCorrelationAgent().agent
        self.vwap = VWAPAgent().agent
        self.news_analysis = NewsAnalysisAgent().agent
        self.data_collection = DataCollectionAgent().agent
        self.validation = ValidationAgent().agent

    def run(self, symbols):
        """Run the analysis for given symbols"""
        # Set up the crew
        crew = Crew(
            agents=[
                self.data_collection,
                self.news_analysis,
                self.trendline_breakout,
                self.fisher_transform,
                self.sector_rotation,
                self.gap_fill,
                self.ma_ribbon,
                self.option_chain,
                self.global_correlation,
                self.vwap,
                self.validation,
                self.supervisor
            ],
            verbose=2,
            memory=True
        )

        # Define tasks
        tasks = self._create_tasks(symbols)
        
        # Execute the crew
        results = crew.kickoff(inputs={'symbols': symbols})
        return results

    def _create_tasks(self, symbols):
        """Create tasks for each agent"""
        tasks = []
        
        # Data collection task
        tasks.append({
            'description': f"Collect real-time market data for {', '.join(symbols)}",
            'agent': self.data_collection,
            'expected_output': f"Clean, normalized market data for {len(symbols)} symbols"
        })
        
        # News analysis task
        tasks.append({
            'description': "Analyze relevant news and sector trends",
            'agent': self.news_analysis,
            'expected_output': "News sentiment scores and sector momentum analysis"
        })
        
        # Strategy analysis tasks
        for symbol in symbols:
            tasks.append({
                'description': f"Analyze {symbol} using Trendline Breakout strategy",
                'agent': self.trendline_breakout,
                'expected_output': f"Trendline Breakout analysis report for {symbol}"
            })
            tasks.append({
                'description': f"Analyze {symbol} using Fisher Transform strategy",
                'agent': self.fisher_transform,
                'expected_output': f"Fisher Transform analysis report for {symbol}"
            })
            # Add tasks for other strategies similarly...
        
        # Validation task
        tasks.append({
            'description': "Validate all strategy outputs against historical performance",
            'agent': self.validation,
            'expected_output': "Backtest results and strategy confidence scores"
        })
        
        # Supervisor task
        tasks.append({
            'description': "Combine all analysis and make final trading decisions",
            'agent': self.supervisor,
            'expected_output': "Final trade recommendations with entry, target, stop-loss"
        })
        
        return tasks

if __name__ == "__main__":
    # Example symbols to analyze
    symbols = ['RELIANCE', 'TATASTEEL', 'HDFCBANK', 'ICICIBANK', 'INFY']
    
    crew = IntradayTradingCrew()
    results = crew.run(symbols)
    
    print("\nFinal Trading Recommendations:")
    for recommendation in results:
        print(f"""
        Stock: {recommendation['stock']} ({recommendation['symbol']})
        Rating: {recommendation['rating']}/10
        Action: {recommendation['action']}
        Buy Price: {recommendation['buy_price']}
        Target: {recommendation['target']}
        Stop Loss: {recommendation['stop_loss']}
        Confidence: {recommendation['confidence']}
        Timeframe: {recommendation['timeframe']}
        """)
2. Strategy Agents
agents/strategy_agents/trendline_breakout_agent.py
python
from crewai import Agent
from langchain.tools import tool
from tools.market_data import MarketData
import numpy as np
import pandas as pd

class TrendlineBreakoutAgent:
    def __init__(self):
        self.agent = Agent(
            role="Trendline Breakout Strategist",
            goal="Identify high-probability breakout trades using trendlines and volume analysis",
            backstory="Specialized in price action and volume analysis with 15 years of experience trading Indian equities",
            tools=[self.analyze_trendline_breakout],
            verbose=True
        )
        self.data_tool = MarketData()

    @tool
    def analyze_trendline_breakout(self, symbol: str) -> dict:
        """Analyze stock using Trendline Breakout strategy"""
        data = self.data_tool.get_intraday_data(symbol, interval='15m')
        data = self.data_tool.calculate_indicators(data)
        
        # Calculate trendline
        highs = data['High'].values
        lows = data['Low'].values
        close = data['Close'].values
        volume = data['Volume'].values
        
        # Simplified trendline detection (in practice would use more sophisticated method)
        resistance = self._detect_resistance(highs)
        support = self._detect_support(lows)
        
        last_close = close[-1]
        avg_volume = np.mean(volume[-20:])
        last_volume = volume[-1]
        
        # Check breakout conditions
        breakout_up = last_close > resistance and last_volume > 1.5 * avg_volume
        breakout_down = last_close < support and last_volume > 1.5 * avg_volume
        
        score = 0
        if breakout_up:
            score = min(8 + (last_volume / avg_volume - 1.5) * 2, 10)  # Scale score based on volume surge
        elif breakout_down:
            score = min(7 + (last_volume / avg_volume - 1.5) * 2, 10)
        
        return {
            'symbol': symbol,
            'strategy': 'Trendline Breakout',
            'score': round(score, 2),
            'recommendation': 'BUY' if breakout_up else 'SELL' if breakout_down else 'HOLD',
            'entry': last_close,
            'stop_loss': support if breakout_up else resistance if breakout_down else None,
            'target': last_close * 1.015 if breakout_up else last_close * 0.985 if breakout_down else None,
            'conditions_met': {
                'breakout': breakout_up or breakout_down,
                'volume_surge': last_volume > 1.5 * avg_volume,
                'trendline_touch': last_close >= resistance or last_close <= support
            }
        }
    
    def _detect_resistance(self, highs, lookback=20):
        """Simplified resistance detection"""
        return max(highs[-lookback:])
    
    def _detect_support(self, lows, lookback=20):
        """Simplified support detection"""
        return min(lows[-lookback:])
agents/strategy_agents/fisher_transform_agent.py
python
from crewai import Agent
from langchain.tools import tool
from tools.market_data import MarketData
import numpy as np
import talib

class FisherTransformAgent:
    def __init__(self):
        self.agent = Agent(
            role="Fisher Transform Strategist",
            goal="Identify mean-reversion opportunities using Fisher Transform and RSI(3)",
            backstory="Quantitative analyst specializing in oscillator-based strategies for intraday trading",
            tools=[self.analyze_fisher_strategy],
            verbose=True
        )
        self.data_tool = MarketData()
    
    @tool
    def analyze_fisher_strategy(self, symbol: str) -> dict:
        """Analyze stock using Fisher Transform strategy"""
        data = self.data_tool.get_intraday_data(symbol, interval='15m')
        data = self.data_tool.calculate_indicators(data)
        
        # Calculate Fisher Transform
        high, low, close = data['High'], data['Low'], data['Close']
        fisher = self._calculate_fisher_transform(high, low, period=10)
        rsi_3 = data['RSI_3'].iloc[-1]
        volume = data['Volume'].iloc[-1]
        avg_volume = data['Volume'].rolling(20).mean().iloc[-1]
        ema_5 = data['5_EMA'].iloc[-1]
        ema_20 = data['20_EMA'].iloc[-1]
        
        # Check conditions
        fisher_value = fisher[-1]
        oversold = fisher_value <= -2 and rsi_3 < 30
        overbought = fisher_value >= 2 and rsi_3 > 70
        volume_ok = volume > 1.5 * avg_volume
        trend_ok = ema_5 > ema_20 if fisher_value <= -2 else ema_5 < ema_20 if fisher_value >= 2 else True
        
        score = 0
        if (oversold or overbought) and volume_ok and trend_ok:
            score = 8 + (abs(fisher_value) - 2) * 1.5  # Scale score based on Fisher extreme
            
        return {
            'symbol': symbol,
            'strategy': 'Fisher Transform',
            'score': min(round(score, 2), 10),
            'recommendation': 'BUY' if oversold else 'SELL' if overbought else 'HOLD',
            'entry': close.iloc[-1],
            'stop_loss': close.iloc[-1] * 0.995 if oversold else close.iloc[-1] * 1.005 if overbought else None,
            'target': close.iloc[-1] * 1.02 if oversold else close.iloc[-1] * 0.98 if overbought else None,
            'conditions_met': {
                'fisher_extreme': oversold or overbought,
                'rsi_confirmation': rsi_3 < 30 or rsi_3 > 70,
                'volume_surge': volume_ok,
                'trend_alignment': trend_ok
            }
        }
    
    def _calculate_fisher_transform(self, high, low, period=10):
        """Calculate Fisher Transform"""
        median_price = (high + low) / 2
        max_price = high.rolling(period).max()
        min_price = low.rolling(period).min()
        
        normalized = (2 * ((median_price - min_price) / (max_price - min_price))) - 1
        normalized = normalized.replace([np.inf, -np.inf], np.nan).fillna(0)
        normalized = np.clip(normalized, -0.999, 0.999)
        
        fisher = 0.5 * np.log((1 + normalized) / (1 - normalized))
        return fisher
3. Other Agents
agents/news_analysis_agent.py
python
from crewai import Agent
from langchain.tools import tool
from tools.moneycontrol_scraper import MoneyControlScraper
from tools.bloomberg_integration import BloombergIntegration
from tools.nlp_processor import NLPProcessor
from datetime import datetime

class NewsAnalysisAgent:
    def __init__(self):
        self.agent = Agent(
            role="Market News Analyst",
            goal="Analyze news sentiment and sector trends to identify market-moving events",
            backstory="Former financial journalist with deep understanding of how news impacts Indian markets",
            tools=[
                self.get_moneycontrol_news,
                self.get_bloomberg_news,
                self.analyze_news_sentiment
            ],
            verbose=True
        )
        self.moneycontrol = MoneyControlScraper()
        self.bloomberg = BloombergIntegration()
        self.nlp = NLPProcessor()
    
    @tool
    def get_moneycontrol_news(self, sectors: list = None) -> list:
        """Get latest news from Moneycontrol for specified sectors"""
        if not sectors:
            sectors = [None]  # Get general news
        news_items = []
        for sector in sectors:
            news_items.extend(self.moneycontrol.get_top_news(sector))
        return news_items
    
    @tool
    def get_bloomberg_news(self, query: str = "Indian markets") -> list:
        """Get relevant news from Bloomberg"""
        return self.bloomberg.search_news(query)
    
    @tool
    def analyze_news_sentiment(self, news_items: list) -> dict:
        """Analyze sentiment of news articles and extract relevant stocks"""
        analyzed = []
        stock_mentions = {}
        
        for item in news_items:
            analysis = self.nlp.analyze_sentiment(item['title'] + " " + item.get('summary', ''))
            analyzed.append({
                'title': item['title'],
                'source': item.get('source', 'moneycontrol'),
                'sentiment': analysis['sentiment'],
                'score': analysis['score'],
                'stocks': analysis['stocks']
            })
            
            for stock in analysis['stocks']:
                if stock not in stock_mentions:
                    stock_mentions[stock] = {
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'neutral': 0
                    }
                stock_mentions[stock]['count'] += 1
                stock_mentions[stock][analysis['sentiment']] += 1
        
        # Calculate overall sentiment for each stock
        stock_sentiment = {}
        for stock, data in stock_mentions.items():
            if data['positive'] > data['negative']:
                sentiment = 'positive'
            elif data['negative'] > data['positive']:
                sentiment = 'negative'
            else:
                sentiment = 'neutral'
            stock_sentiment[stock] = {
                'sentiment': sentiment,
                'score': (data['positive'] - data['negative']) / data['count'],
                'total_mentions': data['count']
            }
        
        return {
            'news_analysis': analyzed,
            'stock_sentiment': stock_sentiment
        }
agents/data_collection_agent.py
python
from crewai import Agent
from langchain.tools import tool
from tools.market_data import MarketData
import yfinance as yf
import pandas as pd

class DataCollectionAgent:
    def __init__(self):
        self.agent = Agent(
            role="Market Data Collector",
            goal="Gather and normalize real-time market data for analysis",
            backstory="Data engineer specializing in financial market data pipelines",
            tools=[
                self.get_intraday_data,
                self.get_historical_data,
                self.get_sector_performance
            ],
            verbose=True
        )
        self.market_data = MarketData()
    
    @tool
    def get_intraday_data(self, symbol: str, interval: str = '15m') -> dict:
        """Get intraday data for a symbol with given interval (5m, 15m, 30m, 1h)"""
        data = self.market_data.get_intraday_data(symbol, interval=interval)
        indicators = self.market_data.calculate_indicators(data)
        return {
            'symbol': symbol,
            'data': indicators.reset_index().to_dict('records'),
            'last_updated': pd.Timestamp.now().isoformat()
        }
    
    @tool
    def get_historical_data(self, symbol: str, period: str = '1y') -> dict:
        """Get historical daily data for backtesting"""
        data = yf.download(f"{symbol}.NS", period=period)
        return {
            'symbol': symbol,
            'data': data.reset_index().to_dict('records'),
            'period': period
        }
    
    @tool
    def get_sector_performance(self) -> dict:
        """Get current performance of all NSE sectors"""
        sectors = {
            'NIFTY_AUTO': '^CNXAUTO',
            'NIFTY_BANK': '^NSEBANK',
            'NIFTY_FIN': '^CNXFINANCE',
            # Add all Nifty sector indices
        }
        
        performance = {}
        for sector, ticker in sectors.items():
            data = yf.download(ticker, period='1d')
            if not data.empty:
                change = (data['Close'].iloc[-1] - data['Open'].iloc[0]) / data['Open'].iloc[0] * 100
                performance[sector] = round(change, 2)
        
        return {
            'sector_performance': performance,
            'top_sectors': sorted(performance.items(), key=lambda x: abs(x[1]), reverse=True)[:3]
        }
agents/validation_agent.py
python
from crewai import Agent
from langchain.tools import tool
import backtrader as bt
import pandas as pd
import numpy as np

class ValidationAgent:
    def __init__(self):
        self.agent = Agent(
            role="Strategy Validation Expert",
            goal="Backtest and validate trading strategies using historical data",
            backstory="Quantitative researcher with expertise in statistical validation of trading strategies",
            tools=[
                self.backtest_strategy,
                self.walk_forward_test,
                self.monte_carlo_simulate
            ],
            verbose=True
        )
    
    @tool
    def backtest_strategy(self, strategy_name: str, params: dict) -> dict:
        """Backtest a trading strategy with given parameters"""
        cerebro = bt.Cerebro()
        
        # Add data feed
        data = self._prepare_data(params['symbol'], params.get('period', '1y'))
        cerebro.adddata(data)
        
        # Add strategy
        if strategy_name == 'Trendline Breakout':
            cerebro.addstrategy(TrendlineBreakoutStrategy, **params)
        elif strategy_name == 'Fisher Transform':
            cerebro.addstrategy(FisherTransformStrategy, **params)
        # Add other strategies...
        
        # Add analyzers
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        # Run backtest
        results = cerebro.run()
        
        # Extract performance metrics
        sharpe = results[0].analyzers.sharpe.get_analysis()
        drawdown = results[0].analyzers.drawdown.get_analysis()
        trades = results[0].analyzers.trades.get_analysis()
        
        return {
            'strategy': strategy_name,
            'parameters': params,
            'sharpe_ratio': sharpe['sharperatio'],
            'max_drawdown': drawdown['max']['drawdown'],
            'win_rate': trades['won']['total'] / trades['total']['total'] if trades['total']['total'] > 0 else 0,
            'profit_factor': trades['won']['pnl']['total'] / abs(trades['lost']['pnl']['total']) if trades['lost']['total'] > 0 else np.inf
        }
    
    def _prepare_data(self, symbol, period):
        """Prepare Backtrader data feed"""
        df = yf.download(f"{symbol}.NS", period=period)
        return bt.feeds.PandasData(dataname=df)
    
    @tool
    def walk_forward_test(self, strategy_name: str, params: dict, windows: int = 5) -> dict:
        """Perform walk-forward analysis on a strategy"""
        results = []
        for i in range(windows):
            # Split data into in-sample and out-of-sample periods
            # Implement actual walk-forward logic
            result = self.backtest_strategy(strategy_name, params)
            results.append(result)
        
        return {
            'strategy': strategy_name,
            'walk_forward_results': results,
            'average_sharpe': np.mean([r['sharpe_ratio'] for r in results]),
            'consistency': len([r for r in results if r['sharpe_ratio'] > 1]) / windows
        }
    
    @tool
    def monte_carlo_simulate(self, strategy_name: str, params: dict, simulations: int = 100) -> dict:
        """Perform Monte Carlo simulation on strategy results"""
        # This would use actual trade history and shuffle it
        return {
            'strategy': strategy_name,
            'probability_profit': 0.85,  # Example value
            'risk_of_ruin': 0.05,      # Example value
            'confidence_interval': (0.7, 1.3)  # Example
        }

# Strategy implementations for Backtrader would be in separate files
4. Supervisor Agent
agents/supervisor_agent.py
python
from crewai import Agent
from langchain.tools import tool
from langchain_openai import ChatOpenAI
import numpy as np

class SupervisorAgent:
    def __init__(self):
        self.agent = Agent(
            role="Senior Trading Strategist",
            goal="Combine insights from all analysis agents to make final trading decisions",
            backstory="With 20+ years of experience in Indian markets, you specialize in combining technical and fundamental factors",
            verbose=True,
            llm=ChatOpenAI(model="gpt-4-turbo", temperature=0),
            tools=[self.evaluate_strategy_combination],
            allow_delegation=False
        )
    
    @tool
    def evaluate_strategy_combination(self, strategy_reports: list, news_analysis: dict = None) -> dict:
        """Combine analysis from multiple strategies and apply final filters"""
        # Group reports by symbol
        symbol_reports = {}
        for report in strategy_reports:
            symbol = report['symbol']
            if symbol not in symbol_reports:
                symbol_reports[symbol] = []
            symbol_reports[symbol].append(report)
        
        final_recommendations = []
        
        for symbol, reports in symbol_reports.items():
            # Calculate combined score (weighted average)
            valid_strategies = [r for r in reports if r['score'] > 5]
            if not valid_strategies:
                continue
                
            weights = np.array([r['score'] for r in valid_strategies])
            weights = weights / weights.sum()  # Normalize
            
            # Get consensus recommendation
            actions = [1 if r['recommendation'] == 'BUY' else -1 if r['recommendation'] == 'SELL' else 0 
                      for r in valid_strategies]
            consensus = np.dot(actions, weights)
            
            # Calculate average targets
            buy_reports = [r for r in valid_strategies if r['recommendation'] == 'BUY']
            sell_reports = [r for r in valid_strategies if r['recommendation'] == 'SELL']
            
            if consensus > 0.3 and buy_reports:  # Strong buy signal
                entry = np.mean([r['entry'] for r in buy_reports])
                stop_loss = min([r['stop_loss'] for r in buy_reports])
                target = np.mean([r['target'] for r in buy_reports])
                confidence = 'High' if consensus > 0.7 else 'Medium'
                action = 'BUY'
            elif consensus < -0.3 and sell_reports:  # Strong sell signal
                entry = np.mean([r['entry'] for r in sell_reports])
                stop_loss = max([r['stop_loss'] for r in sell_reports])
                target = np.mean([r['target'] for r in sell_reports])
                confidence = 'High' if consensus < -0.7 else 'Medium'
                action = 'SELL'
            else:
                continue  # No clear signal
            
            # Apply news filter if available
            if news_analysis and symbol in news_analysis['stock_sentiment']:
                news_sentiment = news_analysis['stock_sentiment'][symbol]['sentiment']
                if (action == 'BUY' and news_sentiment == 'negative') or \
                   (action == 'SELL' and news_sentiment == 'positive'):
                    confidence = 'Low'  # Downgrade confidence on conflicting signals
            
            # Final risk management checks
            risk_reward = (target - entry) / (entry - stop_loss) if action == 'BUY' else \
                          (entry - target) / (stop_loss - entry)
            
            if risk_reward < 1.5:
                confidence = max(confidence, 'Medium')  # Don't allow High confidence for poor R/R
            
            final_recommendations.append({
                'stock': symbol.split('.')[0],  # Remove .NS suffix if present
                'symbol': symbol,
                'rating': round(np.mean([r['score'] for r in valid_strategies]), 2),
                'action': action,
                'buy_price': round(entry, 2),
                'target': round(target, 2),
                'stop_loss': round(stop_loss, 2),
                'confidence': confidence,
                'timeframe': '15min',
                'strategies': [r['strategy'] for r in valid_strategies],
                'risk_reward': round(risk_reward, 2)
            })
        
        return sorted(final_recommendations, key=lambda x: x['rating'], reverse=True)
5. Tools Implementation
tools/moneycontrol_scraper.py
python
import requests
from bs4 import BeautifulSoup
from datetime import datetime
import re

class MoneyControlScraper:
    def __init__(self):
        self.base_url = "https://www.moneycontrol.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def get_top_news(self, sector=None):
        if sector:
            url = f"{self.base_url}/news/{sector}/"
        else:
            url = f"{self.base_url}/news/"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            news_items = []
            for item in soup.select('.clearfix.newslist'):
                title = item.select_one('h2')
                if not title:
                    continue
                
                link = item.select_one('a')
                time_element = item.select_one('.datetime')
                
                news_items.append({
                    'title': title.text.strip(),
                    'link': self.base_url + link['href'] if link and not link['href'].startswith('http') else link['href'],
                    'time': time_element.text.strip() if time_element else '',
                    'sector': sector or 'general'
                })
            
            return news_items[:10]
        except Exception as e:
            print(f"Error fetching Moneycontrol news: {e}")
            return []
    
    def get_stock_news(self, symbol):
        """Get news specific to a stock"""
        url = f"{self.base_url}/stocks/company_info/stock_news.php?sc_id={symbol}"
        try:
            response = requests.get(url, headers=self.headers)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            news_items = []
            for item in soup.select('.PA10 .clearfix'):
                title = item.select_one('a')
                time_element = item.select_one('.PT3')
                
                if title and time_element:
                    news_items.append({
                        'title': title.text.strip(),
                        'link': self.base_url + title['href'],
                        'time': re.sub(r'\s+', ' ', time_element.text).strip(),
                        'source': 'Moneycontrol'
                    })
            
            return news_items[:5]
        except Exception as e:
            print(f"Error fetching stock news for {symbol}: {e}")
            return []