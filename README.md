# Intraday Trading Analyzer

A comprehensive AI-powered intraday trading analysis system using CrewAI with multiple specialized agents for Indian stock markets.

## Overview

This system uses multiple AI agents to analyze Indian stocks using various trading strategies and provides consolidated trading recommendations. Each agent specializes in a specific trading strategy and the supervisor agent combines all insights to make final decisions.

## Features

### Trading Strategies
- **Trendline Breakout**: Identifies breakout opportunities using trendlines and volume analysis
- **Fisher Transform**: Mean-reversion strategy using Fisher Transform and RSI(3)
- **Sector Rotation**: Analyzes sector momentum and relative strength
- **Gap Fill**: Identifies gap fill opportunities in intraday sessions
- **MA Ribbon**: Trend-following using multiple moving average ribbons
- **Option Chain**: Analyzes option chain data for support/resistance levels
- **Global Correlation**: Considers global market correlations
- **VWAP**: Volume Weighted Average Price based strategies

### Core Agents
- **Supervisor Agent**: Combines all analysis and makes final trading decisions
- **News Analysis Agent**: Analyzes market news and sentiment
- **Data Collection Agent**: Gathers and normalizes market data
- **Validation Agent**: Backtests and validates strategies

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd intraday-analyzer
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure API keys:
```bash
cp config/.env.template config/api_keys.env
# Edit config/api_keys.env with your actual API keys
```

## Configuration

### Required API Keys
- **OpenAI API Key**: Required for GPT models used by agents
- **Financial Data APIs**: Optional (Alpha Vantage, Quandl, etc.)
- **News APIs**: Optional (News API, Bloomberg, etc.)

### Configuration Files
- `config/api_keys.env`: API keys and environment variables
- `config/config.py`: Configuration manager
- `requirements.txt`: Python dependencies

## Usage

### Basic Usage
```python
from main import IntradayTradingCrew

# Initialize the crew
crew = IntradayTradingCrew()

# Analyze specific symbols
symbols = ['RELIANCE', 'TATASTEEL', 'HDFCBANK', 'ICICIBANK', 'INFY']
results = crew.run(symbols)

# Print recommendations
for recommendation in results:
    print(f"Stock: {recommendation['stock']}")
    print(f"Action: {recommendation['action']}")
    print(f"Entry: {recommendation['buy_price']}")
    print(f"Target: {recommendation['target']}")
    print(f"Stop Loss: {recommendation['stop_loss']}")
    print(f"Confidence: {recommendation['confidence']}")
    print("---")
```

### Running the Main Script
```bash
python main.py
```

## Project Structure

```
intraday-analyzer/
├── main.py                          # Main application entry point
├── requirements.txt                 # Python dependencies
├── README.md                       # This file
├── config/                         # Configuration files
│   ├── __init__.py
│   ├── config.py                   # Configuration manager
│   ├── api_keys.env               # API keys (not in version control)
│   └── .env.template              # Environment template
├── agents/                         # AI agents
│   ├── __init__.py
│   ├── supervisor_agent.py         # Main supervisor agent
│   ├── news_analysis_agent.py      # News analysis agent
│   ├── data_collection_agent.py    # Data collection agent
│   ├── validation_agent.py         # Strategy validation agent
│   └── strategy_agents/            # Trading strategy agents
│       ├── __init__.py
│       ├── trendline_breakout_agent.py
│       ├── fisher_transform_agent.py
│       ├── sector_rotation_agent.py
│       ├── gap_fill_agent.py
│       ├── ma_ribbon_agent.py
│       ├── option_chain_agent.py
│       ├── global_correlation_agent.py
│       └── vwap_agent.py
└── tools/                          # Utility tools
    ├── __init__.py
    ├── market_data.py              # Market data fetching
    ├── moneycontrol_scraper.py     # News scraping
    ├── bloomberg_integration.py    # Bloomberg integration
    └── nlp_processor.py            # Natural language processing
```

## Strategy Details

### Trendline Breakout
- Detects support and resistance levels
- Identifies breakout patterns with volume confirmation
- Provides entry, stop-loss, and target levels

### Fisher Transform
- Uses Fisher Transform oscillator for mean reversion
- Combines with RSI(3) for confirmation
- Identifies oversold/overbought conditions

### Sector Rotation
- Analyzes sector performance and momentum
- Identifies relative strength opportunities
- Considers sector rotation patterns

### Gap Fill
- Identifies opening gaps in stock prices
- Analyzes gap fill probability
- Provides mean reversion opportunities

### MA Ribbon
- Uses multiple moving averages for trend identification
- Analyzes ribbon alignment and price position
- Includes ADX for trend strength confirmation

### Option Chain Analysis
- Analyzes Put-Call Ratio (PCR)
- Identifies max pain levels
- Determines option-based support/resistance

### Global Correlation
- Considers global market movements
- Analyzes currency impact (USD/INR)
- Factors in international market sentiment

### VWAP Strategy
- Uses Volume Weighted Average Price
- Creates VWAP bands for support/resistance
- Combines with volume analysis

## Risk Management

The system includes built-in risk management features:
- Position sizing limits
- Stop-loss calculations
- Risk-reward ratio analysis
- Maximum daily loss limits

## Backtesting

The validation agent provides backtesting capabilities:
- Historical strategy performance
- Walk-forward analysis
- Monte Carlo simulations
- Performance metrics (Sharpe ratio, drawdown, win rate)

## Development

### Adding New Strategies
1. Create a new agent in `agents/strategy_agents/`
2. Implement the strategy logic with proper scoring
3. Add the agent to `main.py`
4. Update configuration if needed

### Testing
```bash
pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

## Disclaimer

This system is for educational and research purposes only. It is not financial advice. Always consult with qualified financial advisors before making investment decisions. Past performance does not guarantee future results.

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]

## Support

[Add support information here]
