"""
Technical Analysis Agent for CrewAI
Specialized agent for Indian market technical analysis
"""

from crewai_tools import BaseTool
from typing import Dict, Any
import sys
import os
import pandas as pd
import numpy as np
import yfinance as yf

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TechnicalAnalysisAgent(BaseTool):
    name: str = "Indian Market Technical Analyst"
    description: str = """
    Performs comprehensive technical analysis optimized for Indian market conditions.
    Calculates RSI, MACD, Bollinger Bands, and other indicators calibrated for
    Indian stock volatility patterns and intraday trading behaviors.
    
    Specializes in:
    - RSI analysis for Indian market conditions
    - MACD crossover signals
    - Bollinger Band mean reversion
    - Volume analysis for Indian stocks
    - Support/resistance levels
    """
    
    def __init__(self):
        super().__init__()
        self.indian_symbols = {
            'RELIANCE': 'RELIANCE.NS',
            'TCS': 'TCS.NS',
            'HDFCBANK': 'HDFCBANK.NS',
            'ICICIBANK': 'ICICIBANK.NS',
            'INFY': 'INFY.NS',
            'HINDUNILVR': 'HINDUNILVR.NS',
            'ITC': 'ITC.NS',
            'SBIN': 'SBIN.NS',
            'BHARTIARTL': 'BHARTIARTL.NS',
            'KOTAKBANK': 'KOTAKBANK.NS'
        }
    
    def _run(self, stocks_data: str) -> Dict[str, Any]:
        """
        Perform technical analysis on Indian stocks
        
        Args:
            stocks_data: JSON string or comma-separated stock symbols
            
        Returns:
            Dictionary containing technical analysis for each stock
        """
        
        try:
            print(f"📈 Performing technical analysis on Indian stocks...")
            
            # Parse input
            if ',' in stocks_data:
                stock_list = [s.strip() for s in stocks_data.split(',')]
            else:
                stock_list = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY']
            
            technical_results = {}
            
            for symbol in stock_list:
                print(f"   🔍 Analyzing {symbol}...")
                
                try:
                    # Get technical analysis for the stock
                    analysis = self._analyze_stock(symbol)
                    technical_results[symbol] = analysis
                    
                    # Display key metrics
                    if analysis and 'indicators' in analysis:
                        indicators = analysis['indicators']
                        rsi = indicators.get('rsi_14', 0)
                        macd_signal = indicators.get('macd_signal', 'NEUTRAL')
                        bb_position = indicators.get('bb_position', 'MIDDLE')
                        
                        print(f"      📊 RSI: {rsi:.1f} | MACD: {macd_signal} | BB: {bb_position}")
                    
                except Exception as e:
                    print(f"      ❌ Error analyzing {symbol}: {e}")
                    technical_results[symbol] = {'error': str(e)}
            
            # Compile overall technical summary
            technical_summary = {
                'stocks_analyzed': len(technical_results),
                'successful_analysis': len([r for r in technical_results.values() if 'error' not in r]),
                'technical_results': technical_results,
                'market_technical_overview': self._get_market_overview(technical_results),
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            print(f"✅ Technical analysis completed for {len(technical_results)} stocks")
            
            return technical_summary
            
        except Exception as e:
            print(f"❌ Error in technical analysis: {e}")
            return {
                'error': str(e),
                'stocks_analyzed': 0,
                'successful_analysis': 0,
                'technical_results': {},
                'market_technical_overview': {},
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def _analyze_stock(self, symbol: str) -> Dict[str, Any]:
        """Perform technical analysis on a single stock"""
        
        try:
            # Get Yahoo Finance symbol
            yahoo_symbol = self.indian_symbols.get(symbol, f"{symbol}.NS")
            
            # Get recent data for technical analysis
            data = yf.download(yahoo_symbol, period="1mo", interval="15m", progress=False)
            
            if data.empty:
                # Generate sample data for demonstration
                data = self._generate_sample_data(symbol)
            
            # Calculate technical indicators
            indicators = self._calculate_indicators(data)
            
            # Generate signals
            signals = self._generate_signals(indicators, data)
            
            # Calculate support/resistance
            levels = self._calculate_support_resistance(data)
            
            return {
                'symbol': symbol,
                'current_price': float(data['Close'].iloc[-1]),
                'indicators': indicators,
                'signals': signals,
                'support_resistance': levels,
                'volume_analysis': self._analyze_volume(data),
                'trend_analysis': self._analyze_trend(data),
                'data_points': len(data)
            }
            
        except Exception as e:
            return {'error': str(e), 'symbol': symbol}
    
    def _calculate_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators"""
        
        indicators = {}
        
        try:
            # RSI
            indicators['rsi_14'] = self._calculate_rsi(data['Close'], 14).iloc[-1]
            indicators['rsi_3'] = self._calculate_rsi(data['Close'], 3).iloc[-1]
            
            # MACD
            macd_line, macd_signal, macd_histogram = self._calculate_macd(data['Close'])
            indicators['macd'] = macd_line.iloc[-1]
            indicators['macd_signal_line'] = macd_signal.iloc[-1]
            indicators['macd_histogram'] = macd_histogram.iloc[-1]
            indicators['macd_signal'] = 'BUY' if macd_line.iloc[-1] > macd_signal.iloc[-1] else 'SELL'
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(data['Close'])
            current_price = data['Close'].iloc[-1]
            indicators['bb_upper'] = bb_upper.iloc[-1]
            indicators['bb_middle'] = bb_middle.iloc[-1]
            indicators['bb_lower'] = bb_lower.iloc[-1]
            
            # BB Position
            if current_price > bb_upper.iloc[-1]:
                indicators['bb_position'] = 'ABOVE_UPPER'
            elif current_price < bb_lower.iloc[-1]:
                indicators['bb_position'] = 'BELOW_LOWER'
            else:
                indicators['bb_position'] = 'MIDDLE'
            
            # Moving Averages
            indicators['ema_5'] = data['Close'].ewm(span=5).mean().iloc[-1]
            indicators['ema_20'] = data['Close'].ewm(span=20).mean().iloc[-1]
            indicators['sma_50'] = data['Close'].rolling(window=min(50, len(data))).mean().iloc[-1]
            
            # Volume indicators
            indicators['volume_sma'] = data['Volume'].rolling(window=20).mean().iloc[-1]
            indicators['volume_ratio'] = data['Volume'].iloc[-1] / indicators['volume_sma']
            
        except Exception as e:
            indicators['error'] = str(e)
        
        return indicators
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series) -> tuple:
        """Calculate MACD"""
        exp1 = prices.ewm(span=12).mean()
        exp2 = prices.ewm(span=26).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=9).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20) -> tuple:
        """Calculate Bollinger Bands"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * 2)
        lower_band = sma - (std * 2)
        return upper_band, sma, lower_band
    
    def _generate_signals(self, indicators: Dict, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate trading signals based on technical indicators"""
        
        signals = {}
        
        try:
            current_price = data['Close'].iloc[-1]
            rsi_14 = indicators.get('rsi_14', 50)
            rsi_3 = indicators.get('rsi_3', 50)
            bb_position = indicators.get('bb_position', 'MIDDLE')
            macd_signal = indicators.get('macd_signal', 'NEUTRAL')
            volume_ratio = indicators.get('volume_ratio', 1)
            
            # Mean Reversion Signal (proven strategy)
            if rsi_3 < 30 and bb_position == 'BELOW_LOWER' and volume_ratio > 1.2:
                signals['mean_reversion'] = {
                    'signal': 'BUY',
                    'strength': 'HIGH',
                    'reason': 'RSI oversold + below BB lower band + high volume'
                }
            elif rsi_3 > 70 and bb_position == 'ABOVE_UPPER' and volume_ratio > 1.2:
                signals['mean_reversion'] = {
                    'signal': 'SELL',
                    'strength': 'HIGH',
                    'reason': 'RSI overbought + above BB upper band + high volume'
                }
            else:
                signals['mean_reversion'] = {
                    'signal': 'HOLD',
                    'strength': 'LOW',
                    'reason': 'No clear mean reversion signal'
                }
            
            # MACD Crossover Signal
            if macd_signal == 'BUY' and volume_ratio > 1.1 and rsi_14 > 40:
                signals['macd_crossover'] = {
                    'signal': 'BUY',
                    'strength': 'MEDIUM',
                    'reason': 'MACD bullish crossover + volume + RSI support'
                }
            elif macd_signal == 'SELL' or rsi_14 > 75:
                signals['macd_crossover'] = {
                    'signal': 'SELL',
                    'strength': 'MEDIUM',
                    'reason': 'MACD bearish crossover or RSI overbought'
                }
            else:
                signals['macd_crossover'] = {
                    'signal': 'HOLD',
                    'strength': 'LOW',
                    'reason': 'No clear MACD signal'
                }
            
            # Overall signal strength
            buy_signals = sum(1 for s in signals.values() if s['signal'] == 'BUY')
            sell_signals = sum(1 for s in signals.values() if s['signal'] == 'SELL')
            
            if buy_signals > sell_signals:
                signals['overall'] = 'BUY'
            elif sell_signals > buy_signals:
                signals['overall'] = 'SELL'
            else:
                signals['overall'] = 'HOLD'
            
        except Exception as e:
            signals['error'] = str(e)
        
        return signals
    
    def _calculate_support_resistance(self, data: pd.DataFrame) -> Dict[str, float]:
        """Calculate support and resistance levels"""
        
        try:
            high_prices = data['High'].rolling(window=20).max()
            low_prices = data['Low'].rolling(window=20).min()
            
            resistance = high_prices.iloc[-1]
            support = low_prices.iloc[-1]
            current_price = data['Close'].iloc[-1]
            
            return {
                'resistance': float(resistance),
                'support': float(support),
                'current_price': float(current_price),
                'distance_to_resistance': float((resistance - current_price) / current_price * 100),
                'distance_to_support': float((current_price - support) / current_price * 100)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_volume(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns"""
        
        try:
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].rolling(window=20).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume
            
            return {
                'current_volume': int(current_volume),
                'average_volume': int(avg_volume),
                'volume_ratio': float(volume_ratio),
                'volume_trend': 'HIGH' if volume_ratio > 1.5 else 'NORMAL' if volume_ratio > 0.8 else 'LOW'
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_trend(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze price trend"""
        
        try:
            # Calculate trend using moving averages
            ema_5 = data['Close'].ewm(span=5).mean()
            ema_20 = data['Close'].ewm(span=20).mean()
            
            current_price = data['Close'].iloc[-1]
            ema_5_current = ema_5.iloc[-1]
            ema_20_current = ema_20.iloc[-1]
            
            if current_price > ema_5_current > ema_20_current:
                trend = 'STRONG_UPTREND'
            elif current_price > ema_5_current:
                trend = 'UPTREND'
            elif current_price < ema_5_current < ema_20_current:
                trend = 'STRONG_DOWNTREND'
            elif current_price < ema_5_current:
                trend = 'DOWNTREND'
            else:
                trend = 'SIDEWAYS'
            
            return {
                'trend': trend,
                'ema_5': float(ema_5_current),
                'ema_20': float(ema_20_current),
                'current_price': float(current_price)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_market_overview(self, technical_results: Dict) -> Dict[str, Any]:
        """Get overall market technical overview"""
        
        try:
            successful_results = [r for r in technical_results.values() if 'error' not in r]
            
            if not successful_results:
                return {'status': 'insufficient_data'}
            
            # Count signals
            buy_signals = 0
            sell_signals = 0
            hold_signals = 0
            
            for result in successful_results:
                if 'signals' in result and 'overall' in result['signals']:
                    signal = result['signals']['overall']
                    if signal == 'BUY':
                        buy_signals += 1
                    elif signal == 'SELL':
                        sell_signals += 1
                    else:
                        hold_signals += 1
            
            total_signals = buy_signals + sell_signals + hold_signals
            
            if total_signals == 0:
                market_sentiment = 'NEUTRAL'
            elif buy_signals > sell_signals:
                market_sentiment = 'BULLISH'
            elif sell_signals > buy_signals:
                market_sentiment = 'BEARISH'
            else:
                market_sentiment = 'MIXED'
            
            return {
                'market_sentiment': market_sentiment,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'hold_signals': hold_signals,
                'total_analyzed': len(successful_results),
                'signal_distribution': {
                    'bullish_percentage': (buy_signals / total_signals * 100) if total_signals > 0 else 0,
                    'bearish_percentage': (sell_signals / total_signals * 100) if total_signals > 0 else 0
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _generate_sample_data(self, symbol: str) -> pd.DataFrame:
        """Generate sample data when real data is not available"""
        
        base_prices = {
            'RELIANCE': 1500, 'TCS': 3500, 'HDFCBANK': 2000,
            'ICICIBANK': 1400, 'INFY': 1600
        }
        
        base_price = base_prices.get(symbol, 1500)
        
        # Generate 100 data points
        dates = pd.date_range(end=pd.Timestamp.now(), periods=100, freq='15T')
        
        # Generate realistic price movements
        returns = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # Create OHLCV data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            volatility = abs(np.random.normal(0, 0.005))
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = prices[i-1] if i > 0 else price
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                'Open': open_price,
                'High': max(open_price, high, price),
                'Low': min(open_price, low, price),
                'Close': price,
                'Volume': volume
            })
        
        return pd.DataFrame(data, index=dates)
