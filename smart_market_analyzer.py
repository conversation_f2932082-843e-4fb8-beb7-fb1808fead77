#!/usr/bin/env python3
"""
Smart Indian Market Analyzer
- Live analysis during market hours with optimal timing
- Predictive analysis during market closure/holidays
- Automatic mode detection and recommendations
"""

import os
from datetime import datetime, timedelta
import pytz
from dotenv import load_dotenv
from tools.indian_market_data import IndianMarketData
from tools.indian_news_aggregator import IndianNewsAggregator
from tools.nlp_processor import NLPProcessor
import pandas as pd
import numpy as np
import time

# Load environment variables
load_dotenv("config/api_keys.env")

class SmartMarketAnalyzer:
    """Smart analyzer that adapts to market conditions"""
    
    def __init__(self):
        print("🧠 SMART INDIAN MARKET ANALYZER")
        print("=" * 60)
        
        self.market_data = IndianMarketData()
        self.news_aggregator = IndianNewsAggregator()
        self.nlp_processor = NLPProcessor()
        
        # Indian market timings (IST)
        self.ist = pytz.timezone('Asia/Kolkata')
        self.market_open_time = "09:15"
        self.market_close_time = "15:30"
        
        # Best times for live analysis during market hours
        self.optimal_times = [
            "09:30",  # 15 minutes after opening (initial volatility settles)
            "10:00",  # Morning momentum
            "11:30",  # Mid-morning analysis
            "13:00",  # Post-lunch session
            "14:30",  # Pre-closing hour
            "15:15"   # Final 15 minutes
        ]
        
        # Detect current market status
        self.market_status = self.get_detailed_market_status()
        self.display_market_status()
    
    def get_detailed_market_status(self) -> dict:
        """Get detailed market status with timing recommendations"""
        now = datetime.now(self.ist)
        
        # Market hours: Monday-Friday, 9:15 AM - 3:30 PM IST
        is_weekday = now.weekday() < 5
        current_time = now.strftime("%H:%M")
        
        status = {
            'current_time': now.strftime('%Y-%m-%d %H:%M:%S IST'),
            'is_weekday': is_weekday,
            'current_time_only': current_time
        }
        
        if not is_weekday:
            status.update({
                'status': 'WEEKEND',
                'is_live': False,
                'mode': 'PREDICTIVE',
                'next_session': self._get_next_trading_day(now),
                'recommendation': 'Run predictive analysis and prepare watchlist'
            })
        elif current_time < "09:15":
            status.update({
                'status': 'PRE_MARKET',
                'is_live': False,
                'mode': 'PREDICTIVE',
                'next_session': f"Today {self.market_open_time}",
                'recommendation': 'Prepare for market opening with pre-market analysis'
            })
        elif "09:15" <= current_time <= "15:30":
            status.update({
                'status': 'LIVE_MARKET',
                'is_live': True,
                'mode': 'LIVE',
                'next_optimal_time': self._get_next_optimal_time(current_time),
                'recommendation': 'Run live analysis now for real-time trading signals'
            })
        else:
            status.update({
                'status': 'POST_MARKET',
                'is_live': False,
                'mode': 'ANALYSIS',
                'next_session': self._get_next_trading_day(now),
                'recommendation': 'Analyze today\'s performance and prepare for tomorrow'
            })
        
        return status
    
    def display_market_status(self):
        """Display current market status and recommendations"""
        status = self.market_status
        
        status_emojis = {
            'WEEKEND': '🔴',
            'PRE_MARKET': '🟡',
            'LIVE_MARKET': '🟢',
            'POST_MARKET': '🟠'
        }
        
        emoji = status_emojis.get(status['status'], '⚪')
        
        print(f"🏛️ Market Status: {emoji} {status['status']}")
        print(f"⏰ Current Time: {status['current_time']}")
        print(f"🎯 Mode: {status['mode']}")
        print(f"💡 Recommendation: {status['recommendation']}")
        
        if status['is_live']:
            print(f"⚡ LIVE MARKET - Best time for real-time analysis!")
            if 'next_optimal_time' in status:
                print(f"🕐 Next optimal time: {status['next_optimal_time']}")
        else:
            print(f"📅 Next session: {status.get('next_session', 'Next trading day')}")
    
    def _get_next_trading_day(self, current_time):
        """Get next trading day"""
        next_day = current_time + timedelta(days=1)
        while next_day.weekday() >= 5:  # Skip weekends
            next_day += timedelta(days=1)
        return next_day.strftime('%Y-%m-%d 09:15 IST')
    
    def _get_next_optimal_time(self, current_time):
        """Get next optimal time for analysis"""
        for opt_time in self.optimal_times:
            if current_time < opt_time:
                return opt_time
        return "Market closing soon - final analysis time"
    
    def run_live_analysis(self, symbols: list) -> dict:
        """Run live market analysis during trading hours"""
        print(f"\n🔴 LIVE MARKET ANALYSIS")
        print(f"⚡ Real-time data • 📊 Live signals • 🎯 Immediate action")
        print("=" * 60)
        
        results = {
            'mode': 'LIVE',
            'analysis_time': datetime.now(self.ist).strftime('%H:%M:%S IST'),
            'recommendations': [],
            'market_snapshot': {},
            'urgent_alerts': []
        }
        
        # Get live market snapshot
        print("📊 Getting live market snapshot...")
        indices = self.market_data.get_indian_market_indices()
        results['market_snapshot'] = indices
        
        # Analyze each symbol for live trading
        for symbol in symbols:
            print(f"\n🔍 Live analysis: {symbol}")
            
            try:
                # Get real-time quote
                quotes = self.market_data.get_comprehensive_quote(symbol)
                if not quotes:
                    continue
                
                primary_quote = quotes.get('yahoo_finance', list(quotes.values())[0])
                current_price = primary_quote.get('price', 0)
                change_percent = primary_quote.get('change_percent', 0)
                volume = primary_quote.get('volume', 0)
                
                # Live trading signals
                signal = self._generate_live_signal(primary_quote, symbol)
                
                recommendation = {
                    'symbol': symbol,
                    'current_price': current_price,
                    'change_percent': change_percent,
                    'volume': volume,
                    'signal': signal['action'],
                    'urgency': signal['urgency'],
                    'confidence': signal['confidence'],
                    'entry_price': signal.get('entry'),
                    'stop_loss': signal.get('stop_loss'),
                    'target': signal.get('target'),
                    'reason': signal['reason']
                }
                
                results['recommendations'].append(recommendation)
                
                # Check for urgent alerts
                if signal['urgency'] == 'HIGH':
                    results['urgent_alerts'].append(f"{symbol}: {signal['action']} - {signal['reason']}")
                
                print(f"   💰 Price: ₹{current_price:.2f} ({change_percent:+.2f}%)")
                print(f"   🎯 Signal: {signal['action']} ({signal['urgency']} urgency)")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return results
    
    def run_predictive_analysis(self, symbols: list) -> dict:
        """Run predictive analysis during market closure"""
        print(f"\n🔮 PREDICTIVE MARKET ANALYSIS")
        print(f"📈 Tomorrow's prep • 📰 News impact • 🎯 Watchlist ready")
        print("=" * 60)
        
        results = {
            'mode': 'PREDICTIVE',
            'analysis_time': datetime.now(self.ist).strftime('%Y-%m-%d %H:%M:%S IST'),
            'predictions': [],
            'watchlist': [],
            'news_impact': {},
            'sector_outlook': {}
        }
        
        # Get comprehensive news for prediction
        print("📰 Analyzing news for market impact...")
        news = self.news_aggregator.get_comprehensive_indian_news(10)
        news_sentiment = self.nlp_processor.analyze_sentiment(
            ' '.join([item['title'] for item in news])
        )
        results['news_impact'] = {
            'sentiment': news_sentiment.get('sentiment', 'neutral'),
            'key_news': [item['title'] for item in news[:3]],
            'stocks_in_news': news_sentiment.get('stocks', [])
        }
        
        # Analyze each symbol for tomorrow's potential
        for symbol in symbols:
            print(f"\n🔍 Predictive analysis: {symbol}")
            
            try:
                # Get last available data
                quotes = self.market_data.get_comprehensive_quote(symbol)
                if not quotes:
                    continue
                
                primary_quote = quotes.get('yahoo_finance', list(quotes.values())[0])
                
                # Generate prediction
                prediction = self._generate_prediction(primary_quote, symbol, news_sentiment)
                
                prediction_result = {
                    'symbol': symbol,
                    'last_price': primary_quote.get('price', 0),
                    'prediction': prediction['direction'],
                    'confidence': prediction['confidence'],
                    'expected_range': prediction['range'],
                    'key_levels': prediction['levels'],
                    'watch_for': prediction['triggers'],
                    'sector_impact': prediction.get('sector_impact', 'neutral')
                }
                
                results['predictions'].append(prediction_result)
                
                # Add to watchlist if high potential
                if prediction['confidence'] in ['High', 'Medium'] and prediction['direction'] != 'SIDEWAYS':
                    results['watchlist'].append({
                        'symbol': symbol,
                        'reason': prediction['reason'],
                        'action': 'BUY' if prediction['direction'] == 'UP' else 'SELL',
                        'target_entry': prediction['levels']['entry']
                    })
                
                print(f"   📊 Last Price: ₹{primary_quote.get('price', 0):.2f}")
                print(f"   🔮 Prediction: {prediction['direction']} ({prediction['confidence']})")
                print(f"   🎯 Watch for: {prediction['triggers']}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return results
    
    def _generate_live_signal(self, quote_data: dict, symbol: str) -> dict:
        """Generate live trading signal"""
        price = quote_data.get('price', 0)
        change_percent = quote_data.get('change_percent', 0)
        volume = quote_data.get('volume', 0)
        
        # Live signal logic
        if abs(change_percent) > 3 and volume > 1000000:
            if change_percent > 3:
                return {
                    'action': 'STRONG_BUY',
                    'urgency': 'HIGH',
                    'confidence': 'High',
                    'entry': price,
                    'stop_loss': price * 0.97,
                    'target': price * 1.05,
                    'reason': f'Strong upward momentum (+{change_percent:.1f}%) with high volume'
                }
            else:
                return {
                    'action': 'STRONG_SELL',
                    'urgency': 'HIGH',
                    'confidence': 'High',
                    'entry': price,
                    'stop_loss': price * 1.03,
                    'target': price * 0.95,
                    'reason': f'Strong downward momentum ({change_percent:.1f}%) with high volume'
                }
        elif abs(change_percent) > 1.5:
            if change_percent > 1.5:
                return {
                    'action': 'BUY',
                    'urgency': 'MEDIUM',
                    'confidence': 'Medium',
                    'entry': price,
                    'stop_loss': price * 0.98,
                    'target': price * 1.03,
                    'reason': f'Moderate upward movement (+{change_percent:.1f}%)'
                }
            else:
                return {
                    'action': 'SELL',
                    'urgency': 'MEDIUM',
                    'confidence': 'Medium',
                    'entry': price,
                    'stop_loss': price * 1.02,
                    'target': price * 0.97,
                    'reason': f'Moderate downward movement ({change_percent:.1f}%)'
                }
        else:
            return {
                'action': 'HOLD',
                'urgency': 'LOW',
                'confidence': 'Low',
                'reason': f'Minimal movement ({change_percent:.1f}%) - wait for clearer signal'
            }
    
    def _generate_prediction(self, quote_data: dict, symbol: str, news_sentiment: dict) -> dict:
        """Generate prediction for next trading session"""
        price = quote_data.get('price', 0)
        change_percent = quote_data.get('change_percent', 0)
        
        # Prediction logic based on recent performance and news
        sentiment = news_sentiment.get('sentiment', 'neutral')
        stocks_mentioned = news_sentiment.get('stocks', [])
        
        symbol_clean = symbol.replace('.NS', '').upper()
        is_in_news = symbol_clean in stocks_mentioned
        
        # Base prediction on recent trend
        if change_percent > 1:
            direction = 'UP'
            confidence = 'High' if is_in_news and sentiment == 'positive' else 'Medium'
        elif change_percent < -1:
            direction = 'DOWN'
            confidence = 'High' if is_in_news and sentiment == 'negative' else 'Medium'
        else:
            direction = 'SIDEWAYS'
            confidence = 'Low'
        
        # Adjust based on news sentiment
        if is_in_news:
            if sentiment == 'positive' and direction != 'UP':
                direction = 'UP'
                confidence = 'Medium'
            elif sentiment == 'negative' and direction != 'DOWN':
                direction = 'DOWN'
                confidence = 'Medium'
        
        # Calculate expected range
        volatility = abs(change_percent) / 100
        expected_range = {
            'low': price * (1 - volatility * 1.5),
            'high': price * (1 + volatility * 1.5)
        }
        
        return {
            'direction': direction,
            'confidence': confidence,
            'range': expected_range,
            'levels': {
                'entry': price,
                'support': price * 0.98,
                'resistance': price * 1.02
            },
            'triggers': f"Break above ₹{price * 1.01:.2f}" if direction == 'UP' else f"Break below ₹{price * 0.99:.2f}" if direction == 'DOWN' else "Range trading",
            'reason': f"Based on {change_percent:+.1f}% trend" + (f" + {sentiment} news" if is_in_news else "")
        }
    
    def print_results(self, results: dict):
        """Print analysis results based on mode"""
        if results['mode'] == 'LIVE':
            self._print_live_results(results)
        else:
            self._print_predictive_results(results)
    
    def _print_live_results(self, results: dict):
        """Print live analysis results"""
        print(f"\n{'='*70}")
        print(f"🔴 LIVE TRADING SIGNALS - {results['analysis_time']}")
        print(f"{'='*70}")
        
        # Urgent alerts first
        if results['urgent_alerts']:
            print("🚨 URGENT ALERTS:")
            for alert in results['urgent_alerts']:
                print(f"   ⚡ {alert}")
            print()
        
        # Market snapshot
        print("📊 MARKET SNAPSHOT:")
        snapshot = results['market_snapshot']
        for index, data in list(snapshot.items())[:3]:  # Show top 3 indices
            emoji = "🟢" if data['change'] >= 0 else "🔴"
            print(f"   {index}: {data['value']:.2f} ({data['change']:+.2f}) {emoji}")
        print()
        
        # Trading recommendations
        print("🎯 LIVE TRADING SIGNALS:")
        for i, rec in enumerate(results['recommendations'], 1):
            urgency_emoji = "🚨" if rec['urgency'] == 'HIGH' else "⚡" if rec['urgency'] == 'MEDIUM' else "📊"
            
            print(f"\n{i}. {rec['symbol']} - {rec['signal']} {urgency_emoji}")
            print(f"   💰 Price: ₹{rec['current_price']:.2f} ({rec['change_percent']:+.2f}%)")
            print(f"   🎯 Confidence: {rec['confidence']}")
            if rec.get('entry_price'):
                print(f"   📍 Entry: ₹{rec['entry_price']:.2f}")
            if rec.get('target'):
                print(f"   🎯 Target: ₹{rec['target']:.2f}")
            if rec.get('stop_loss'):
                print(f"   🛡️ Stop: ₹{rec['stop_loss']:.2f}")
            print(f"   💡 Reason: {rec['reason']}")
    
    def _print_predictive_results(self, results: dict):
        """Print predictive analysis results"""
        print(f"\n{'='*70}")
        print(f"🔮 PREDICTIVE ANALYSIS - {results['analysis_time']}")
        print(f"{'='*70}")
        
        # News impact
        news = results['news_impact']
        sentiment_emoji = "🟢" if news['sentiment'] == 'positive' else "🔴" if news['sentiment'] == 'negative' else "🟡"
        print(f"📰 NEWS SENTIMENT: {news['sentiment'].upper()} {sentiment_emoji}")
        print("   Key Headlines:")
        for headline in news['key_news']:
            print(f"   • {headline}")
        if news['stocks_in_news']:
            print(f"   📈 Stocks in news: {', '.join(news['stocks_in_news'])}")
        print()
        
        # Watchlist
        if results['watchlist']:
            print("⭐ TOMORROW'S WATCHLIST:")
            for item in results['watchlist']:
                print(f"   🎯 {item['symbol']} - {item['action']} at ₹{item['target_entry']:.2f}")
                print(f"      Reason: {item['reason']}")
            print()
        
        # Predictions
        print("🔮 STOCK PREDICTIONS:")
        for i, pred in enumerate(results['predictions'], 1):
            direction_emoji = "🟢" if pred['prediction'] == 'UP' else "🔴" if pred['prediction'] == 'DOWN' else "🟡"
            
            print(f"\n{i}. {pred['symbol']} - {pred['prediction']} {direction_emoji}")
            print(f"   💰 Last Price: ₹{pred['last_price']:.2f}")
            print(f"   🎯 Confidence: {pred['confidence']}")
            print(f"   📊 Expected Range: ₹{pred['expected_range']['low']:.2f} - ₹{pred['expected_range']['high']:.2f}")
            print(f"   🔍 Watch for: {pred['watch_for']}")
    
    def get_optimal_run_times(self) -> dict:
        """Get optimal times to run the analyzer"""
        return {
            'live_market_times': [
                "09:30 - Initial momentum analysis",
                "10:00 - Morning trend confirmation", 
                "11:30 - Mid-morning opportunities",
                "13:00 - Post-lunch session analysis",
                "14:30 - Pre-closing hour signals",
                "15:15 - Final trading opportunities"
            ],
            'predictive_times': [
                "16:00 - Post-market analysis",
                "20:00 - Evening news impact analysis",
                "08:30 - Pre-market preparation"
            ],
            'best_live_time': "10:00 AM IST (most reliable signals)",
            'best_predictive_time': "8:30 AM IST (pre-market prep)"
        }


def main():
    """Main function with smart mode detection"""
    # Indian blue-chip stocks
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY']
    
    analyzer = SmartMarketAnalyzer()
    
    print(f"\n🎯 ANALYSIS MODE: {analyzer.market_status['mode']}")
    
    if analyzer.market_status['is_live']:
        # Live market analysis
        results = analyzer.run_live_analysis(symbols)
    else:
        # Predictive analysis
        results = analyzer.run_predictive_analysis(symbols)
    
    # Print results
    analyzer.print_results(results)
    
    # Show optimal timing recommendations
    print(f"\n{'='*70}")
    print("⏰ OPTIMAL RUN TIMES")
    print(f"{'='*70}")
    
    optimal_times = analyzer.get_optimal_run_times()
    
    if analyzer.market_status['is_live']:
        print("🔴 LIVE MARKET - Best times today:")
        for time_info in optimal_times['live_market_times']:
            print(f"   • {time_info}")
    else:
        print("🔮 MARKET CLOSED - Best times for analysis:")
        for time_info in optimal_times['predictive_times']:
            print(f"   • {time_info}")
    
    print(f"\n💡 RECOMMENDATION:")
    if analyzer.market_status['is_live']:
        print("   🔴 Market is LIVE! Run this script every 30-60 minutes for fresh signals")
        print("   ⚡ Best time: 10:00 AM IST for most reliable trading signals")
    else:
        print("   🔮 Market is closed. Use this analysis to prepare for next session")
        print("   📅 Best time: 8:30 AM IST tomorrow for pre-market preparation")


if __name__ == "__main__":
    main()
