import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
from typing import Dict, List
import json

class IndianNewsAggregator:
    """Comprehensive Indian financial news from multiple Indian sources"""
    
    def __init__(self):
        # Indian financial news sources
        self.sources = {
            'moneycontrol': {
                'base_url': 'https://www.moneycontrol.com',
                'news_url': 'https://www.moneycontrol.com/news/business/markets/',
                'sectors_url': 'https://www.moneycontrol.com/news/business/markets/sector-watch/'
            },
            'bloomberg_quint': {
                'base_url': 'https://www.bloombergquint.com',
                'markets_url': 'https://www.bloombergquint.com/markets',
                'india_url': 'https://www.bloombergquint.com/india'
            },
            'investing_india': {
                'base_url': 'https://in.investing.com',
                'news_url': 'https://in.investing.com/news/stock-market-news',
                'analysis_url': 'https://in.investing.com/analysis/stock-market'
            },
            'economic_times': {
                'base_url': 'https://economictimes.indiatimes.com',
                'markets_url': 'https://economictimes.indiatimes.com/markets',
                'stocks_url': 'https://economictimes.indiatimes.com/markets/stocks'
            },
            'business_standard': {
                'base_url': 'https://www.business-standard.com',
                'markets_url': 'https://www.business-standard.com/markets',
                'companies_url': 'https://www.business-standard.com/companies'
            }
        }
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        # Indian stock and sector keywords
        self.indian_stocks = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR',
            'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK', 'LT', 'ASIANPAINT',
            'MARUTI', 'TATASTEEL', 'AXISBANK', 'BAJFINANCE', 'WIPRO',
            'ULTRACEMCO', 'NESTLEIND', 'POWERGRID', 'ONGC', 'NTPC',
            'COALINDIA', 'DRREDDY', 'SUNPHARMA', 'TECHM', 'HCLTECH'
        ]
        
        self.indian_sectors = [
            'banking', 'it', 'pharma', 'auto', 'fmcg', 'metal', 'oil', 'gas',
            'telecom', 'realty', 'infrastructure', 'power', 'textiles'
        ]
        
        print("🇮🇳 Indian News Aggregator Initialized")
        print("📰 Sources: MoneyControl, Bloomberg Quint, Investing.com, ET, Business Standard")
    
    def get_moneycontrol_news(self, limit: int = 10) -> List[Dict]:
        """Get news from MoneyControl"""
        try:
            response = requests.get(
                self.sources['moneycontrol']['news_url'], 
                headers=self.headers, 
                timeout=10
            )
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                news_items = []
                
                # MoneyControl specific selectors
                articles = soup.find_all(['div', 'article'], class_=re.compile(r'news|story|article'))
                
                for article in articles[:limit]:
                    title_elem = article.find(['h1', 'h2', 'h3', 'h4', 'a'])
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        if len(title) > 15:
                            link_elem = article.find('a')
                            link = self._normalize_url(link_elem.get('href') if link_elem else '', 
                                                    self.sources['moneycontrol']['base_url'])
                            
                            # Extract time if available
                            time_elem = article.find(class_=re.compile(r'time|date'))
                            time_text = time_elem.get_text(strip=True) if time_elem else ""
                            
                            news_items.append({
                                'title': title,
                                'url': link,
                                'source': 'MoneyControl',
                                'time': time_text,
                                'category': self._categorize_news(title),
                                'stocks_mentioned': self._extract_stock_mentions(title),
                                'timestamp': datetime.now().isoformat()
                            })
                
                return news_items
                
        except Exception as e:
            print(f"⚠️ MoneyControl news error: {e}")
        
        return []
    
    def get_bloomberg_quint_news(self, limit: int = 10) -> List[Dict]:
        """Get news from Bloomberg Quint"""
        try:
            response = requests.get(
                self.sources['bloomberg_quint']['markets_url'], 
                headers=self.headers, 
                timeout=10
            )
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                news_items = []
                
                # Bloomberg Quint specific selectors
                articles = soup.find_all(['article', 'div'], class_=re.compile(r'story|card|article'))
                
                for article in articles[:limit]:
                    title_elem = article.find(['h1', 'h2', 'h3', 'h4'])
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        if len(title) > 15:
                            link_elem = article.find('a')
                            link = self._normalize_url(link_elem.get('href') if link_elem else '', 
                                                    self.sources['bloomberg_quint']['base_url'])
                            
                            # Extract summary if available
                            summary_elem = article.find(['p', 'div'], class_=re.compile(r'summary|excerpt|description'))
                            summary = summary_elem.get_text(strip=True)[:200] if summary_elem else ""
                            
                            news_items.append({
                                'title': title,
                                'summary': summary,
                                'url': link,
                                'source': 'Bloomberg Quint',
                                'category': self._categorize_news(title),
                                'stocks_mentioned': self._extract_stock_mentions(title + " " + summary),
                                'timestamp': datetime.now().isoformat()
                            })
                
                return news_items
                
        except Exception as e:
            print(f"⚠️ Bloomberg Quint news error: {e}")
        
        return []
    
    def get_investing_india_news(self, limit: int = 10) -> List[Dict]:
        """Get news from Investing.com India"""
        try:
            response = requests.get(
                self.sources['investing_india']['news_url'], 
                headers=self.headers, 
                timeout=10
            )
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                news_items = []
                
                # Investing.com specific selectors
                articles = soup.find_all(['article', 'div'], class_=re.compile(r'article|news|story'))
                
                for article in articles[:limit]:
                    title_elem = article.find(['h1', 'h2', 'h3', 'a'])
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        if len(title) > 15:
                            link_elem = article.find('a')
                            link = self._normalize_url(link_elem.get('href') if link_elem else '', 
                                                    self.sources['investing_india']['base_url'])
                            
                            news_items.append({
                                'title': title,
                                'url': link,
                                'source': 'Investing.com India',
                                'category': self._categorize_news(title),
                                'stocks_mentioned': self._extract_stock_mentions(title),
                                'timestamp': datetime.now().isoformat()
                            })
                
                return news_items
                
        except Exception as e:
            print(f"⚠️ Investing.com India news error: {e}")
        
        return []
    
    def get_comprehensive_indian_news(self, limit_per_source: int = 5) -> List[Dict]:
        """Get comprehensive news from all Indian sources"""
        print("📰 Fetching comprehensive Indian market news...")
        
        all_news = []
        
        # Get news from all sources
        sources_methods = [
            ('MoneyControl', self.get_moneycontrol_news),
            ('Bloomberg Quint', self.get_bloomberg_quint_news),
            ('Investing India', self.get_investing_india_news)
        ]
        
        for source_name, method in sources_methods:
            try:
                news = method(limit_per_source)
                if news:
                    all_news.extend(news)
                    print(f"✅ {source_name}: {len(news)} articles")
                else:
                    print(f"⚠️ {source_name}: No articles retrieved")
            except Exception as e:
                print(f"❌ {source_name}: Error - {e}")
        
        # If no news retrieved, use fallback
        if not all_news:
            all_news = self._get_fallback_indian_news()
            print("📰 Using fallback Indian market news")
        
        # Sort by relevance and recency
        all_news = self._rank_news_by_relevance(all_news)
        
        return all_news[:limit_per_source * 3]  # Return top articles
    
    def get_sector_specific_news(self, sector: str, limit: int = 5) -> List[Dict]:
        """Get sector-specific news"""
        all_news = self.get_comprehensive_indian_news(limit * 2)
        
        # Filter by sector
        sector_news = []
        sector_keywords = {
            'banking': ['bank', 'banking', 'hdfc', 'icici', 'sbi', 'axis', 'kotak'],
            'it': ['it', 'software', 'tech', 'infy', 'tcs', 'wipro', 'hcl'],
            'pharma': ['pharma', 'drug', 'medicine', 'sun pharma', 'dr reddy'],
            'auto': ['auto', 'car', 'maruti', 'tata motors', 'bajaj', 'hero'],
            'fmcg': ['fmcg', 'consumer', 'unilever', 'itc', 'nestle'],
            'metal': ['metal', 'steel', 'tata steel', 'jsw', 'hindalco'],
            'oil': ['oil', 'petroleum', 'ongc', 'reliance', 'bpcl', 'hpcl']
        }
        
        keywords = sector_keywords.get(sector.lower(), [sector.lower()])
        
        for news in all_news:
            title_lower = news['title'].lower()
            if any(keyword in title_lower for keyword in keywords):
                news['sector'] = sector
                sector_news.append(news)
        
        return sector_news[:limit]
    
    def get_stock_specific_news(self, symbol: str, limit: int = 5) -> List[Dict]:
        """Get news specific to a stock"""
        all_news = self.get_comprehensive_indian_news(limit * 2)
        
        # Filter by stock symbol or company name
        stock_news = []
        symbol_clean = symbol.replace('.NS', '').upper()
        
        # Company name mapping
        company_names = {
            'RELIANCE': ['reliance', 'ril'],
            'TCS': ['tcs', 'tata consultancy'],
            'HDFCBANK': ['hdfc bank', 'hdfc'],
            'ICICIBANK': ['icici bank', 'icici'],
            'INFY': ['infosys', 'infy'],
            'TATASTEEL': ['tata steel', 'tata'],
            'MARUTI': ['maruti', 'maruti suzuki'],
            'ITC': ['itc'],
            'SBIN': ['sbi', 'state bank']
        }
        
        search_terms = company_names.get(symbol_clean, [symbol_clean.lower()])
        search_terms.append(symbol_clean.lower())
        
        for news in all_news:
            title_lower = news['title'].lower()
            if any(term in title_lower for term in search_terms):
                news['target_stock'] = symbol
                stock_news.append(news)
        
        return stock_news[:limit]
    
    def _normalize_url(self, url: str, base_url: str) -> str:
        """Normalize URL to absolute URL"""
        if not url:
            return ""
        if url.startswith('http'):
            return url
        elif url.startswith('/'):
            return base_url + url
        else:
            return base_url + '/' + url
    
    def _categorize_news(self, title: str) -> str:
        """Categorize news based on title"""
        title_lower = title.lower()
        
        categories = {
            'earnings': ['earnings', 'profit', 'loss', 'revenue', 'results', 'quarterly'],
            'market': ['market', 'nifty', 'sensex', 'index', 'trading'],
            'policy': ['rbi', 'policy', 'government', 'budget', 'tax'],
            'sector': ['sector', 'industry', 'banking', 'it', 'pharma', 'auto'],
            'global': ['global', 'us', 'china', 'fed', 'international'],
            'ipo': ['ipo', 'listing', 'public offering'],
            'merger': ['merger', 'acquisition', 'deal', 'takeover']
        }
        
        for category, keywords in categories.items():
            if any(keyword in title_lower for keyword in keywords):
                return category
        
        return 'general'
    
    def _extract_stock_mentions(self, text: str) -> List[str]:
        """Extract Indian stock mentions from text"""
        text_upper = text.upper()
        mentioned_stocks = []
        
        for stock in self.indian_stocks:
            if stock in text_upper:
                mentioned_stocks.append(stock)
        
        return mentioned_stocks
    
    def _rank_news_by_relevance(self, news_list: List[Dict]) -> List[Dict]:
        """Rank news by relevance to Indian markets"""
        def relevance_score(news):
            score = 0
            title = news['title'].lower()
            
            # Higher score for market-related keywords
            market_keywords = ['nifty', 'sensex', 'market', 'stock', 'share', 'trading']
            score += sum(2 for keyword in market_keywords if keyword in title)
            
            # Higher score for stock mentions
            score += len(news.get('stocks_mentioned', [])) * 3
            
            # Higher score for important categories
            important_categories = ['earnings', 'market', 'policy']
            if news.get('category') in important_categories:
                score += 5
            
            # Prefer recent news (if timestamp available)
            if 'timestamp' in news:
                try:
                    news_time = datetime.fromisoformat(news['timestamp'].replace('Z', '+00:00'))
                    hours_old = (datetime.now() - news_time.replace(tzinfo=None)).total_seconds() / 3600
                    if hours_old < 24:
                        score += max(0, 10 - hours_old)
                except:
                    pass
            
            return score
        
        return sorted(news_list, key=relevance_score, reverse=True)
    
    def _get_fallback_indian_news(self) -> List[Dict]:
        """Fallback Indian market news"""
        return [
            {
                'title': 'Indian equity markets show strong performance amid global uncertainty',
                'url': 'https://www.moneycontrol.com/news/business/markets/',
                'source': 'MoneyControl',
                'category': 'market',
                'stocks_mentioned': ['NIFTY', 'SENSEX'],
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'Banking sector leads gains as RBI maintains accommodative stance',
                'url': 'https://www.bloombergquint.com/markets',
                'source': 'Bloomberg Quint',
                'category': 'sector',
                'stocks_mentioned': ['HDFCBANK', 'ICICIBANK', 'SBIN'],
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'IT stocks rally on strong Q3 earnings outlook and dollar strength',
                'url': 'https://in.investing.com/news/stock-market-news',
                'source': 'Investing.com India',
                'category': 'earnings',
                'stocks_mentioned': ['INFY', 'TCS', 'WIPRO'],
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'FII inflows boost market sentiment as Nifty approaches record highs',
                'url': 'https://economictimes.indiatimes.com/markets',
                'source': 'Economic Times',
                'category': 'market',
                'stocks_mentioned': ['NIFTY'],
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'Auto sector shows recovery signs with festive season demand pickup',
                'url': 'https://www.business-standard.com/markets',
                'source': 'Business Standard',
                'category': 'sector',
                'stocks_mentioned': ['MARUTI', 'TATAMOTORS'],
                'timestamp': datetime.now().isoformat()
            }
        ]
