#!/usr/bin/env python3
"""
CrewAI Indian Stock Market Analyzer
Agentic AI system for finding best intraday stocks in Indian market
Maintains exact same output as the original system
"""

import os
from datetime import datetime
import pytz
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from crewai_agents.indian_data_agent import IndianDataAgent
from crewai_agents.indian_news_agent import IndianNewsAgent
from crewai_agents.technical_analysis_agent import TechnicalAnalysisAgent
from crewai_agents.strategy_execution_agent import StrategyExecutionAgent
from crewai_agents.market_timing_agent import MarketTimingAgent
from crewai_agents.risk_management_agent import RiskManagementAgent
from crewai_agents.report_generation_agent import ReportGenerationAgent
import json

# Load environment variables
load_dotenv("config/api_keys.env")

class CrewAIIndianAnalyzer:
    """CrewAI-powered Indian stock market analyzer"""
    
    def __init__(self):
        print("🤖 CREWAI INDIAN STOCK MARKET ANALYZER")
        print("=" * 60)
        print("🇮🇳 Agentic AI for Best Intraday Indian Stocks")
        print("🧠 Multiple specialized AI agents working together")
        
        # Initialize IST timezone
        self.ist = pytz.timezone('Asia/Kolkata')
        
        # Indian stocks to analyze
        self.indian_stocks = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY',
            'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK'
        ]
        
        # Initialize agents
        self.agents = self._create_agents()
        
        # Check market status
        self.market_status = self._get_market_status()
        self._display_market_status()
    
    def _get_market_status(self) -> dict:
        """Get current Indian market status"""
        now = datetime.now(self.ist)
        is_weekday = now.weekday() < 5
        current_time = now.strftime("%H:%M")
        
        status = {
            'current_time': now.strftime('%Y-%m-%d %H:%M:%S IST'),
            'is_weekday': is_weekday,
            'current_time_only': current_time
        }
        
        if not is_weekday:
            status.update({
                'status': 'WEEKEND',
                'is_live': False,
                'mode': 'PREDICTIVE',
                'recommendation': 'Run predictive analysis and prepare watchlist'
            })
        elif current_time < "09:15":
            status.update({
                'status': 'PRE_MARKET',
                'is_live': False,
                'mode': 'PREDICTIVE',
                'recommendation': 'Prepare for market opening with pre-market analysis'
            })
        elif "09:15" <= current_time <= "15:30":
            status.update({
                'status': 'LIVE_MARKET',
                'is_live': True,
                'mode': 'LIVE',
                'recommendation': 'Run live analysis now for real-time trading signals'
            })
        else:
            status.update({
                'status': 'POST_MARKET',
                'is_live': False,
                'mode': 'ANALYSIS',
                'recommendation': 'Analyze today\'s performance and prepare for tomorrow'
            })
        
        return status
    
    def _display_market_status(self):
        """Display current market status"""
        status = self.market_status
        
        status_emojis = {
            'WEEKEND': '🔴',
            'PRE_MARKET': '🟡',
            'LIVE_MARKET': '🟢',
            'POST_MARKET': '🟠'
        }
        
        emoji = status_emojis.get(status['status'], '⚪')
        
        print(f"🏛️ Market Status: {emoji} {status['status']}")
        print(f"⏰ Current Time: {status['current_time']}")
        print(f"🎯 Mode: {status['mode']}")
        print(f"💡 Recommendation: {status['recommendation']}")
    
    def _create_agents(self) -> dict:
        """Create specialized CrewAI agents"""
        
        # 1. Indian Data Collection Agent
        data_agent = Agent(
            role='Indian Market Data Specialist',
            goal='Collect comprehensive real-time data from all major Indian financial sources',
            backstory="""You are an expert in Indian stock market data collection. 
            You specialize in gathering data from NSE, BSE, MoneyControl, Yahoo Finance, 
            and other Indian financial platforms. You understand Indian market timings, 
            holidays, and trading patterns.""",
            verbose=True,
            allow_delegation=False,
            tools=[IndianDataAgent()]
        )
        
        # 2. Indian News Analysis Agent
        news_agent = Agent(
            role='Indian Financial News Analyst',
            goal='Analyze news sentiment from Indian financial media and global sources affecting Indian markets',
            backstory="""You are a specialist in Indian financial news analysis. 
            You monitor MoneyControl, Bloomberg Quint, Economic Times, Business Standard, 
            and global news affecting Indian markets. You understand the impact of 
            RBI policies, government decisions, and global events on Indian stocks.""",
            verbose=True,
            allow_delegation=False,
            tools=[IndianNewsAgent()]
        )
        
        # 3. Technical Analysis Agent
        technical_agent = Agent(
            role='Indian Market Technical Analyst',
            goal='Perform comprehensive technical analysis optimized for Indian market conditions',
            backstory="""You are an expert technical analyst specializing in Indian stocks. 
            You understand Indian market volatility patterns, sector rotations, and 
            intraday trading behaviors. You use RSI, MACD, Bollinger Bands, and other 
            indicators calibrated for Indian market conditions.""",
            verbose=True,
            allow_delegation=False,
            tools=[TechnicalAnalysisAgent()]
        )
        
        # 4. Strategy Execution Agent
        strategy_agent = Agent(
            role='Indian Intraday Strategy Specialist',
            goal='Execute proven strategies for finding best intraday opportunities in Indian stocks',
            backstory="""You are a specialist in Indian intraday trading strategies. 
            You focus on Mean Reversion, MACD Crossover, and Momentum strategies 
            that have been backtested and proven profitable on Indian stocks like 
            RELIANCE, INFY, and TCS.""",
            verbose=True,
            allow_delegation=False,
            tools=[StrategyExecutionAgent()]
        )
        
        # 5. Market Timing Agent
        timing_agent = Agent(
            role='Indian Market Timing Expert',
            goal='Determine optimal execution times for Indian market trading',
            backstory="""You are an expert in Indian market timing. You know that 
            8:45 AM IST is best for pre-market analysis, 10:15 AM IST is the golden 
            hour for execution, and you understand NSE/BSE trading patterns throughout 
            the day.""",
            verbose=True,
            allow_delegation=False,
            tools=[MarketTimingAgent()]
        )
        
        # 6. Risk Management Agent
        risk_agent = Agent(
            role='Indian Market Risk Manager',
            goal='Provide risk management and position sizing for Indian stock trades',
            backstory="""You are a risk management specialist for Indian markets. 
            You understand Indian market volatility, provide appropriate stop-losses, 
            position sizing, and risk-adjusted returns. You ensure trades are 
            profitable while managing downside risk.""",
            verbose=True,
            allow_delegation=False,
            tools=[RiskManagementAgent()]
        )
        
        # 7. Report Generation Agent
        report_agent = Agent(
            role='Indian Market Report Specialist',
            goal='Generate comprehensive reports matching the exact format of the original system',
            backstory="""You are responsible for generating the final analysis reports. 
            You ensure the output format exactly matches the original system with 
            proper emojis, formatting, and Indian market specific recommendations. 
            You maintain consistency with the proven system output.""",
            verbose=True,
            allow_delegation=False,
            tools=[ReportGenerationAgent()]
        )
        
        return {
            'data': data_agent,
            'news': news_agent,
            'technical': technical_agent,
            'strategy': strategy_agent,
            'timing': timing_agent,
            'risk': risk_agent,
            'report': report_agent
        }
    
    def _create_tasks(self, analysis_mode: str) -> list:
        """Create tasks based on market status"""
        
        tasks = []
        
        # Task 1: Data Collection
        data_task = Task(
            description=f"""
            Collect comprehensive Indian market data for analysis.
            Mode: {analysis_mode}
            Stocks: {', '.join(self.indian_stocks)}
            
            Requirements:
            - Get real-time/latest prices from Indian sources
            - Collect market indices (Nifty, Sensex, sector indices)
            - Gather volume and technical indicators
            - Ensure data quality and completeness
            
            Output: Structured data dictionary with all market information
            """,
            agent=self.agents['data'],
            expected_output="Dictionary containing comprehensive market data for all specified Indian stocks"
        )
        
        # Task 2: News Analysis
        news_task = Task(
            description=f"""
            Analyze Indian financial news and sentiment.
            Mode: {analysis_mode}
            
            Requirements:
            - Collect news from MoneyControl, Bloomberg Quint, ET
            - Analyze sentiment impact on Indian markets
            - Identify stocks mentioned in news
            - Assess global news impact on Indian markets
            
            Output: News sentiment analysis with stock-specific impacts
            """,
            agent=self.agents['news'],
            expected_output="News sentiment analysis with positive/negative/neutral classification and affected stocks"
        )
        
        # Task 3: Technical Analysis
        technical_task = Task(
            description=f"""
            Perform technical analysis on Indian stocks.
            Mode: {analysis_mode}
            
            Requirements:
            - Calculate RSI, MACD, Bollinger Bands for each stock
            - Identify support/resistance levels
            - Analyze volume patterns
            - Generate technical signals
            
            Dependencies: Requires data from data collection task
            Output: Technical analysis results for each stock
            """,
            agent=self.agents['technical'],
            expected_output="Technical analysis results with indicators and signals for each stock",
            context=[data_task]
        )
        
        # Task 4: Strategy Execution
        strategy_task = Task(
            description=f"""
            Execute proven Indian market strategies.
            Mode: {analysis_mode}
            
            Requirements:
            - Apply Mean Reversion strategy (proven 80.95% returns on RELIANCE)
            - Apply MACD Crossover strategy (proven 76.14% returns on TCS)
            - Generate BUY/SELL/HOLD signals with confidence levels
            - Provide entry/exit prices
            
            Dependencies: Requires technical analysis and news sentiment
            Output: Strategy signals with confidence levels and prices
            """,
            agent=self.agents['strategy'],
            expected_output="Trading signals with BUY/SELL/HOLD recommendations, confidence levels, and target prices",
            context=[technical_task, news_task]
        )
        
        # Task 5: Market Timing
        timing_task = Task(
            description=f"""
            Determine optimal execution timing.
            Mode: {analysis_mode}
            Current Status: {self.market_status['status']}
            
            Requirements:
            - Assess current market timing
            - Provide urgency levels (HIGH/MEDIUM/LOW)
            - Recommend next optimal execution time
            - Consider Indian market hours and patterns
            
            Output: Timing recommendations with urgency levels
            """,
            agent=self.agents['timing'],
            expected_output="Timing analysis with urgency levels and optimal execution recommendations"
        )
        
        # Task 6: Risk Management
        risk_task = Task(
            description=f"""
            Provide risk management for all trading signals.
            Mode: {analysis_mode}
            
            Requirements:
            - Calculate appropriate stop-loss levels
            - Determine position sizing (20% per trade)
            - Assess risk-reward ratios
            - Provide drawdown estimates
            
            Dependencies: Requires strategy signals
            Output: Risk management parameters for each signal
            """,
            agent=self.agents['risk'],
            expected_output="Risk management parameters including stop-loss, position size, and risk metrics",
            context=[strategy_task]
        )
        
        # Task 7: Report Generation
        report_task = Task(
            description=f"""
            Generate final analysis report matching original system format.
            Mode: {analysis_mode}
            
            Requirements:
            - Maintain exact same output format as original system
            - Include all emojis and formatting
            - Provide Indian market specific recommendations
            - Ensure consistency with proven system output
            - Include urgent alerts, market snapshot, and trading signals
            
            Dependencies: All previous tasks
            Output: Formatted report identical to original system
            """,
            agent=self.agents['report'],
            expected_output="Complete formatted analysis report matching original system output exactly",
            context=[data_task, news_task, technical_task, strategy_task, timing_task, risk_task]
        )
        
        tasks = [data_task, news_task, technical_task, strategy_task, timing_task, risk_task, report_task]
        
        return tasks
    
    def run_analysis(self) -> dict:
        """Run the complete CrewAI analysis"""
        
        print(f"\n🚀 STARTING CREWAI ANALYSIS")
        print(f"🎯 Mode: {self.market_status['mode']}")
        print(f"⏰ Time: {self.market_status['current_time']}")
        print("=" * 60)
        
        # Create tasks based on current market status
        tasks = self._create_tasks(self.market_status['mode'])
        
        # Create and run the crew
        crew = Crew(
            agents=list(self.agents.values()),
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
        
        # Execute the crew
        print("🤖 Executing CrewAI agents...")
        result = crew.kickoff()
        
        return {
            'mode': self.market_status['mode'],
            'analysis_time': self.market_status['current_time'],
            'result': result,
            'market_status': self.market_status
        }
    
    def display_results(self, results: dict):
        """Display results in the exact same format as original system"""
        
        print(f"\n{'='*70}")
        if results['mode'] == 'LIVE':
            print(f"🔴 LIVE TRADING SIGNALS - {results['analysis_time']}")
        else:
            print(f"🔮 PREDICTIVE ANALYSIS - {results['analysis_time']}")
        print(f"{'='*70}")
        
        # The report generation agent should have formatted this exactly like the original
        print(results['result'])
        
        # Add optimal timing recommendations
        print(f"\n{'='*70}")
        print("⏰ OPTIMAL RUN TIMES")
        print(f"{'='*70}")
        
        if results['mode'] == 'LIVE':
            print("🔴 LIVE MARKET - Best times today:")
            print("   • 9:25 AM IST - First live check")
            print("   • 10:15 AM IST - Prime execution time")
            print("   • 1:15 PM IST - Post-lunch opportunities")
            print("   • 2:45 PM IST - Pre-closing analysis")
        else:
            print("🔮 MARKET CLOSED - Best times for analysis:")
            print("   • 4:00 PM IST - Post-market analysis")
            print("   • 8:30 PM IST - Evening news impact analysis")
            print("   • 8:45 AM IST - Pre-market preparation")
        
        print(f"\n💡 RECOMMENDATION:")
        if results['mode'] == 'LIVE':
            print("   🔴 Market is LIVE! Execute high-urgency signals immediately")
            print("   ⚡ Best execution window: Within 15 minutes of signal generation")
        else:
            print("   🔮 Market is closed. Use this analysis to prepare for next session")
            print("   📅 Focus on watchlist and key levels for tomorrow")


def main():
    """Main function for CrewAI Indian market analysis"""
    
    # Initialize CrewAI analyzer
    analyzer = CrewAIIndianAnalyzer()
    
    # Run comprehensive analysis
    results = analyzer.run_analysis()
    
    # Display results in original format
    analyzer.display_results(results)
    
    print(f"\n✅ CrewAI analysis completed!")
    print(f"🤖 {len(analyzer.agents)} specialized agents worked together")
    print(f"🇮🇳 Focused on Indian market intraday opportunities")
    print(f"🎯 Maintaining exact same output as original system")


if __name__ == "__main__":
    main()
