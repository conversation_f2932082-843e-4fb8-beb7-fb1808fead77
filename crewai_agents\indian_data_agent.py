"""
Indian Data Collection Agent for CrewAI
Specialized agent for collecting comprehensive Indian market data
"""

from crewai_tools import BaseTool
from typing import Dict, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.indian_market_data import IndianMarketData

class IndianDataAgent(BaseTool):
    name: str = "Indian Market Data Collector"
    description: str = """
    Collects comprehensive real-time data from all major Indian financial sources including:
    - NSE India (official exchange data)
    - Yahoo Finance (reliable Indian stock prices)
    - MoneyControl (Indian market news and data)
    - Bloomberg Quint (market analysis)
    - Investing.com India (economic data)
    
    Provides real-time prices, market indices, volume data, and technical indicators
    for Indian stocks with focus on intraday trading opportunities.
    """
    
    def __init__(self):
        super().__init__()
        self.market_data = IndianMarketData()
    
    def _run(self, stocks: str = "RELIANCE,TCS,HDFCBANK,ICICIBANK,INFY") -> Dict[str, Any]:
        """
        Collect comprehensive Indian market data
        
        Args:
            stocks: Comma-separated list of Indian stock symbols
            
        Returns:
            Dictionary containing comprehensive market data
        """
        
        try:
            stock_list = [s.strip() for s in stocks.split(',')]
            
            print(f"📊 Collecting Indian market data for {len(stock_list)} stocks...")
            
            # Get market status
            market_status = self.market_data.get_indian_market_status()
            
            # Get major Indian indices
            indices = self.market_data.get_indian_market_indices()
            
            # Collect data for each stock
            stock_data = {}
            for symbol in stock_list:
                print(f"   📈 Fetching {symbol}...")
                quotes = self.market_data.get_comprehensive_quote(symbol)
                if quotes:
                    stock_data[symbol] = quotes
                    
                    # Get primary quote for display
                    primary_quote = quotes.get('yahoo_finance', list(quotes.values())[0] if quotes else {})
                    price = primary_quote.get('price', 0)
                    change_percent = primary_quote.get('change_percent', 0)
                    
                    change_emoji = "🟢" if change_percent >= 0 else "🔴"
                    print(f"      💰 Price: ₹{price:.2f} ({change_percent:+.2f}%) {change_emoji}")
            
            # Compile comprehensive data
            comprehensive_data = {
                'market_status': market_status,
                'indices': indices,
                'stocks': stock_data,
                'data_sources': ['NSE India', 'Yahoo Finance', 'MoneyControl', 'Bloomberg Quint'],
                'timestamp': market_status.get('current_time', ''),
                'total_stocks': len(stock_data),
                'successful_fetches': len([s for s in stock_data.values() if s])
            }
            
            print(f"✅ Data collection completed: {len(stock_data)} stocks from {len(comprehensive_data['data_sources'])} sources")
            
            return comprehensive_data
            
        except Exception as e:
            print(f"❌ Error in Indian data collection: {e}")
            return {
                'error': str(e),
                'market_status': {'status': 'ERROR'},
                'indices': {},
                'stocks': {},
                'data_sources': [],
                'timestamp': '',
                'total_stocks': 0,
                'successful_fetches': 0
            }
    
    def get_market_indices(self) -> Dict[str, Any]:
        """Get Indian market indices"""
        try:
            return self.market_data.get_indian_market_indices()
        except Exception as e:
            print(f"❌ Error fetching indices: {e}")
            return {}
    
    def get_stock_quote(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive quote for a single stock"""
        try:
            return self.market_data.get_comprehensive_quote(symbol)
        except Exception as e:
            print(f"❌ Error fetching {symbol}: {e}")
            return {}
    
    def get_market_status(self) -> Dict[str, Any]:
        """Get current Indian market status"""
        try:
            return self.market_data.get_indian_market_status()
        except Exception as e:
            print(f"❌ Error fetching market status: {e}")
            return {'status': 'ERROR', 'error': str(e)}
