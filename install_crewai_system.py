#!/usr/bin/env python3
"""
CrewAI Indian Stock Market Analyzer Installation Script
Installs and sets up the complete agentic AI system
"""

import subprocess
import sys
import os
from pathlib import Path

def install_crewai_dependencies():
    """Install CrewAI and all required dependencies"""
    
    print("🤖 INSTALLING CREWAI INDIAN STOCK MARKET ANALYZER")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("crewai_agents"):
        print("❌ Error: crewai_agents directory not found")
        print("   Please run this script from the project root directory")
        return False
    
    # Install dependencies
    dependencies = [
        "crewai==0.28.8",
        "crewai-tools==0.1.6", 
        "langchain==0.1.20",
        "langchain-openai==0.1.7",
        "langchain-community==0.0.38",
        "openai==1.30.5",
        "yfinance==0.2.18",
        "pandas==2.0.3",
        "numpy==1.24.3",
        "requests==2.31.0",
        "beautifulsoup4==4.12.2",
        "lxml==4.9.3",
        "textblob==0.17.1",
        "vaderSentiment==3.3.2",
        "python-dotenv==1.0.0",
        "pytz==2023.3",
        "colorama==0.4.6"
    ]
    
    print("📦 Installing CrewAI dependencies...")
    
    for i, package in enumerate(dependencies, 1):
        print(f"   {i}/{len(dependencies)} Installing {package.split('==')[0]}...")
        
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"      ✅ {package.split('==')[0]} installed successfully")
            
        except subprocess.CalledProcessError as e:
            print(f"      ❌ Failed to install {package}")
            print(f"         Error: {e.stderr}")
            return False
    
    print("✅ All CrewAI dependencies installed successfully!")
    return True

def setup_environment():
    """Setup environment for CrewAI"""
    
    print("\n⚙️ Setting up CrewAI environment...")
    
    # Create config directory if it doesn't exist
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Check if API keys file exists
    api_keys_file = config_dir / "api_keys.env"
    
    if not api_keys_file.exists():
        print("🔑 Creating API keys configuration...")
        
        # Create basic API keys template
        api_template = """# CrewAI Indian Stock Market Analyzer - API Keys
# OpenAI API Key (required for CrewAI agents)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Other API keys for enhanced functionality
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FINNHUB_API_KEY=your_finnhub_key_here
NEWS_API_KEY=your_news_api_key_here

# CrewAI Configuration
CREWAI_TELEMETRY_OPT_OUT=true
"""
        
        with open(api_keys_file, 'w') as f:
            f.write(api_template)
        
        print(f"   ✅ Created {api_keys_file}")
        print("   📝 Please edit config/api_keys.env and add your OpenAI API key")
    else:
        print("   ✅ API keys file already exists")
    
    # Test imports
    print("\n🧪 Testing CrewAI imports...")
    
    try:
        import crewai
        print("   ✅ CrewAI imported successfully")
        
        from crewai import Agent, Task, Crew
        print("   ✅ CrewAI core components imported")
        
        from crewai_tools import BaseTool
        print("   ✅ CrewAI tools imported")
        
        print("✅ CrewAI environment setup complete!")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("   🔧 Try running the installation again")
        return False

def create_crewai_launcher():
    """Create launcher script for CrewAI system"""
    
    print("\n🚀 Creating CrewAI launcher...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
CrewAI Indian Stock Market Analyzer Launcher
Quick launcher for the agentic AI system
"""

import os
import sys
from pathlib import Path

def main():
    """Launch CrewAI Indian Stock Market Analyzer"""
    
    print("🤖 LAUNCHING CREWAI INDIAN STOCK MARKET ANALYZER")
    print("=" * 60)
    
    # Check if CrewAI is installed
    try:
        import crewai
        print("✅ CrewAI detected")
    except ImportError:
        print("❌ CrewAI not installed")
        print("   Run: python install_crewai_system.py")
        return
    
    # Check API keys
    api_keys_file = Path("config/api_keys.env")
    if not api_keys_file.exists():
        print("❌ API keys file not found")
        print("   Run: python install_crewai_system.py")
        return
    
    # Launch the main CrewAI system
    try:
        from crewai_indian_analyzer import CrewAIIndianAnalyzer
        
        analyzer = CrewAIIndianAnalyzer()
        results = analyzer.run_analysis()
        analyzer.display_results(results)
        
    except Exception as e:
        print(f"❌ Error launching CrewAI system: {e}")
        print("   Falling back to original system...")
        
        # Fallback to original system
        try:
            from smart_market_analyzer import SmartMarketAnalyzer
            
            print("🔄 Using original smart analyzer as fallback")
            fallback_analyzer = SmartMarketAnalyzer()
            
            if fallback_analyzer.market_status['is_live']:
                results = fallback_analyzer.run_live_analysis(['RELIANCE', 'TCS', 'INFY'])
            else:
                results = fallback_analyzer.run_predictive_analysis(['RELIANCE', 'TCS', 'INFY'])
            
            fallback_analyzer.print_results(results)
            
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {fallback_error}")

if __name__ == "__main__":
    main()
'''
    
    with open("launch_crewai.py", 'w') as f:
        f.write(launcher_content)
    
    print("   ✅ Created launch_crewai.py")
    print("   🚀 Use: python launch_crewai.py")

def test_crewai_system():
    """Test the CrewAI system"""
    
    print("\n🧪 Testing CrewAI system...")
    
    try:
        # Test agent imports
        from crewai_agents.indian_data_agent import IndianDataAgent
        from crewai_agents.indian_news_agent import IndianNewsAgent
        from crewai_agents.technical_analysis_agent import TechnicalAnalysisAgent
        from crewai_agents.strategy_execution_agent import StrategyExecutionAgent
        from crewai_agents.market_timing_agent import MarketTimingAgent
        from crewai_agents.risk_management_agent import RiskManagementAgent
        from crewai_agents.report_generation_agent import ReportGenerationAgent
        
        print("   ✅ All CrewAI agents imported successfully")
        
        # Test basic functionality
        data_agent = IndianDataAgent()
        print("   ✅ Data agent created")
        
        timing_agent = MarketTimingAgent()
        print("   ✅ Timing agent created")
        
        print("✅ CrewAI system test passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ CrewAI system test failed: {e}")
        return False

def display_next_steps():
    """Display next steps for user"""
    
    print(f"\n{'='*60}")
    print("🎉 CREWAI INSTALLATION COMPLETE!")
    print(f"{'='*60}")
    
    print("\n📋 NEXT STEPS:")
    print("1. 🔑 Edit config/api_keys.env and add your OpenAI API key")
    print("2. 🚀 Run: python launch_crewai.py")
    print("3. 🎯 Or run: python crewai_indian_analyzer.py")
    
    print("\n🤖 CREWAI FEATURES:")
    print("✅ 7 specialized AI agents working together")
    print("✅ Same output format as original system")
    print("✅ Enhanced intelligence and reasoning")
    print("✅ Automatic fallback to original system")
    
    print("\n⏰ OPTIMAL USAGE:")
    print("🌅 8:45 AM IST - Pre-market preparation")
    print("🚀 10:15 AM IST - Prime execution time")
    print("📈 1:15 PM IST - Afternoon opportunities")
    
    print("\n🎯 The agentic AI system is ready for Indian stock market analysis!")

def main():
    """Main installation function"""
    
    print("🇮🇳 CrewAI Indian Stock Market Analyzer Installation")
    print("🤖 Converting to Agentic AI System")
    
    # Step 1: Install dependencies
    if not install_crewai_dependencies():
        print("❌ Installation failed at dependency installation")
        return
    
    # Step 2: Setup environment
    if not setup_environment():
        print("❌ Installation failed at environment setup")
        return
    
    # Step 3: Create launcher
    create_crewai_launcher()
    
    # Step 4: Test system
    if not test_crewai_system():
        print("⚠️ System test failed, but installation completed")
        print("   The system should still work with manual testing")
    
    # Step 5: Display next steps
    display_next_steps()

if __name__ == "__main__":
    main()
