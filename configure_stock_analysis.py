#!/usr/bin/env python3
"""
Configure Stock Analysis
Interactive script to configure which stocks to analyze
"""

import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.nifty_stocks_fetcher import NiftyStocksFetcher

def display_configuration_options():
    """Display available configuration options"""
    
    print("📊 STOCK ANALYSIS CONFIGURATION OPTIONS")
    print("=" * 50)
    
    print("\n🎯 INDEX TYPES:")
    print("1. nifty_50     - Top 50 stocks (Nifty 50)")
    print("2. nifty_100    - Top 100 stocks (Nifty 100)")
    print("3. nifty_500    - Broader market (Nifty 500 sample)")
    print("4. top_liquid   - Most liquid stocks for intraday")
    print("5. sector_wise  - Stocks from multiple sectors")
    
    print("\n📈 STOCK COUNT OPTIONS:")
    print("• 10-20 stocks  - Quick analysis (faster)")
    print("• 30-50 stocks  - Balanced analysis (recommended)")
    print("• 50-100 stocks - Comprehensive analysis (slower)")
    print("• 100+ stocks   - Full market analysis (very slow)")
    
    print("\n⚡ PERFORMANCE CONSIDERATIONS:")
    print("• More stocks = More comprehensive analysis")
    print("• More stocks = Longer execution time")
    print("• Recommended: 30-50 stocks for optimal balance")

def get_user_configuration():
    """Get configuration from user input"""
    
    print("\n🔧 CONFIGURE YOUR ANALYSIS")
    print("=" * 30)
    
    # Get index type
    print("\n📊 Select Index Type:")
    print("1. Nifty 50 (50 stocks)")
    print("2. Nifty 100 (100 stocks) [RECOMMENDED]")
    print("3. Nifty 500 (sample)")
    print("4. Top Liquid (most liquid stocks)")
    print("5. Sector-wise (balanced sectors)")
    
    while True:
        try:
            choice = input("\nEnter choice (1-5) [default: 2]: ").strip()
            
            if not choice:
                choice = "2"
            
            index_map = {
                "1": "nifty_50",
                "2": "nifty_100", 
                "3": "nifty_500",
                "4": "top_liquid",
                "5": "sector_wise"
            }
            
            if choice in index_map:
                index_type = index_map[choice]
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n❌ Configuration cancelled.")
            return None
    
    # Get stock count
    print(f"\n📈 Selected: {index_type.upper()}")
    
    while True:
        try:
            max_stocks_input = input("Enter maximum stocks to analyze [default: 50]: ").strip()
            
            if not max_stocks_input:
                max_stocks = 50
            else:
                max_stocks = int(max_stocks_input)
                
                if max_stocks < 5:
                    print("❌ Minimum 5 stocks required.")
                    continue
                elif max_stocks > 200:
                    print("⚠️ Warning: >200 stocks will be very slow. Limiting to 200.")
                    max_stocks = 200
            
            break
            
        except ValueError:
            print("❌ Please enter a valid number.")
        except KeyboardInterrupt:
            print("\n❌ Configuration cancelled.")
            return None
    
    return {
        'index_type': index_type,
        'max_stocks': max_stocks
    }

def preview_stock_selection(config):
    """Preview the stocks that will be analyzed"""
    
    print(f"\n🔍 PREVIEWING STOCK SELECTION")
    print("=" * 40)
    
    try:
        fetcher = NiftyStocksFetcher()
        
        index_type = config['index_type']
        max_stocks = config['max_stocks']
        
        print(f"📊 Index Type: {index_type.upper()}")
        print(f"📈 Max Stocks: {max_stocks}")
        
        if index_type == 'top_liquid':
            stocks = fetcher.get_top_liquid_stocks(max_stocks)
        elif index_type == 'sector_wise':
            sector_stocks = fetcher.get_sector_wise_stocks()
            stocks = []
            for sector, sector_list in sector_stocks.items():
                stocks.extend(sector_list[:5])  # Top 5 from each sector
            stocks = stocks[:max_stocks]
        else:
            stocks = fetcher.get_intraday_enabled_stocks(index_type)[:max_stocks]
        
        print(f"\n✅ Found {len(stocks)} stocks")
        
        # Display first 20 stocks
        print(f"\n📋 First 20 stocks:")
        for i, stock in enumerate(stocks[:20], 1):
            print(f"   {i:2d}. {stock}")
        
        if len(stocks) > 20:
            print(f"   ... and {len(stocks) - 20} more stocks")
        
        # Show sector distribution if available
        if index_type == 'sector_wise':
            print(f"\n🏭 Sector Distribution:")
            sector_stocks = fetcher.get_sector_wise_stocks()
            for sector, sector_list in sector_stocks.items():
                count = len([s for s in stocks if s in sector_list])
                if count > 0:
                    print(f"   {sector}: {count} stocks")
        
        return stocks
        
    except Exception as e:
        print(f"❌ Error previewing stocks: {e}")
        return []

def save_configuration(config, stocks):
    """Save configuration to file"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    config_content = f"""# Stock Analysis Configuration
# Generated: {timestamp}

INDEX_TYPE = "{config['index_type']}"
MAX_STOCKS = {config['max_stocks']}
TOTAL_STOCKS_FOUND = {len(stocks)}

# Stock List
STOCKS = {stocks}

# Configuration Summary
# Index Type: {config['index_type'].upper()}
# Max Stocks: {config['max_stocks']}
# Actual Stocks: {len(stocks)}
# Performance: {'Fast' if len(stocks) <= 20 else 'Medium' if len(stocks) <= 50 else 'Slow'}
"""
    
    try:
        with open("stock_analysis_config.py", "w") as f:
            f.write(config_content)
        
        print(f"\n💾 Configuration saved to: stock_analysis_config.py")
        return True
        
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        return False

def test_configuration(config):
    """Test the configuration with agentic analyzer"""
    
    print(f"\n🧪 TESTING CONFIGURATION")
    print("=" * 30)
    
    try:
        from agentic_indian_analyzer import AgenticIndianAnalyzer
        
        print("🤖 Initializing agentic analyzer...")
        analyzer = AgenticIndianAnalyzer()
        
        # Update configuration
        analyzer.update_stock_configuration(
            index_type=config['index_type'],
            max_stocks=config['max_stocks']
        )
        
        print(f"✅ Configuration applied successfully!")
        print(f"📊 Analyzer will process {len(analyzer.indian_stocks)} stocks")
        print(f"🎯 Index Type: {analyzer.config['index_type'].upper()}")
        
        # Ask if user wants to run full analysis
        run_analysis = input("\n❓ Run full analysis now? (y/N): ").strip().lower()
        
        if run_analysis == 'y':
            print("\n🚀 Running full analysis...")
            results = analyzer.run_analysis()
            analyzer.display_results(results)
        else:
            print("✅ Configuration ready. Run analysis with:")
            print("   python agentic_indian_analyzer.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def main():
    """Main configuration function"""
    
    print("🇮🇳 INDIAN STOCK MARKET ANALYZER")
    print("🔧 STOCK ANALYSIS CONFIGURATION")
    print("=" * 50)
    
    # Display options
    display_configuration_options()
    
    # Get user configuration
    config = get_user_configuration()
    
    if not config:
        print("❌ Configuration cancelled.")
        return
    
    # Preview stock selection
    stocks = preview_stock_selection(config)
    
    if not stocks:
        print("❌ No stocks found with current configuration.")
        return
    
    # Confirm configuration
    print(f"\n✅ CONFIGURATION SUMMARY")
    print("=" * 30)
    print(f"📊 Index Type: {config['index_type'].upper()}")
    print(f"📈 Max Stocks: {config['max_stocks']}")
    print(f"🎯 Actual Stocks: {len(stocks)}")
    print(f"⚡ Performance: {'Fast' if len(stocks) <= 20 else 'Medium' if len(stocks) <= 50 else 'Slow'}")
    
    confirm = input(f"\n❓ Proceed with this configuration? (Y/n): ").strip().lower()
    
    if confirm == 'n':
        print("❌ Configuration cancelled.")
        return
    
    # Save configuration
    if save_configuration(config, stocks):
        print("✅ Configuration saved successfully!")
    
    # Test configuration
    test_config = input(f"\n❓ Test configuration with agentic analyzer? (Y/n): ").strip().lower()
    
    if test_config != 'n':
        test_configuration(config)
    
    print(f"\n🎉 Configuration complete!")
    print(f"🚀 Ready to analyze {len(stocks)} Indian stocks!")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Run analysis: python agentic_indian_analyzer.py")
    print(f"2. Best times: 8:45 AM IST (pre-market), 10:15 AM IST (prime time)")
    print(f"3. Monitor results and adjust configuration as needed")

if __name__ == "__main__":
    main()
