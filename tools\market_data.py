import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Try to import talib, but continue without it if not available
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("Warning: TA-Lib not available. Using simplified technical indicators.")

class MarketData:
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
    
    def get_intraday_data(self, symbol: str, interval: str = '15m', period: str = '1d') -> pd.DataFrame:
        """Get intraday data for a symbol"""
        cache_key = f"{symbol}_{interval}_{period}"
        
        # Check cache
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_timeout:
                return cached_data
        
        try:
            # Add .NS suffix for NSE stocks if not present
            ticker = f"{symbol}.NS" if not symbol.endswith('.NS') else symbol
            data = yf.download(ticker, period=period, interval=interval, progress=False)
            
            if data.empty:
                # Generate dummy data if real data is not available
                data = self._generate_dummy_data(symbol, interval, period)
            
            # Cache the data
            self.cache[cache_key] = (data, datetime.now())
            return data
            
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return self._generate_dummy_data(symbol, interval, period)
    
    def get_daily_data(self, symbol: str, period: str = '1y') -> pd.DataFrame:
        """Get daily data for a symbol"""
        try:
            ticker = f"{symbol}.NS" if not symbol.endswith('.NS') else symbol
            data = yf.download(ticker, period=period, progress=False)
            
            if data.empty:
                data = self._generate_dummy_daily_data(symbol, period)
            
            return data
            
        except Exception as e:
            print(f"Error fetching daily data for {symbol}: {e}")
            return self._generate_dummy_daily_data(symbol, period)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        if data.empty:
            return data
        
        df = data.copy()
        
        try:
            # Moving Averages
            df['5_EMA'] = df['Close'].ewm(span=5).mean()
            df['20_EMA'] = df['Close'].ewm(span=20).mean()
            df['50_SMA'] = df['Close'].rolling(window=50).mean()
            
            # RSI
            df['RSI_14'] = self._calculate_rsi(df['Close'], 14)
            df['RSI_3'] = self._calculate_rsi(df['Close'], 3)
            
            # MACD
            exp1 = df['Close'].ewm(span=12).mean()
            exp2 = df['Close'].ewm(span=26).mean()
            df['MACD'] = exp1 - exp2
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            
            # Bollinger Bands
            bb_middle = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Middle'] = bb_middle
            df['BB_Upper'] = bb_middle + (bb_std * 2)
            df['BB_Lower'] = bb_middle - (bb_std * 2)
            
            # Volume indicators
            volume_sma = df['Volume'].rolling(window=20).mean()
            df['Volume_SMA'] = volume_sma
            df['Volume_Ratio'] = df['Volume'] / volume_sma
            
            # ADX (simplified)
            df['ADX_14'] = self._calculate_adx(df, 14)
            
        except Exception as e:
            print(f"Error calculating indicators: {e}")
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_adx(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate ADX (simplified)"""
        if TALIB_AVAILABLE:
            try:
                high = df['High'].values
                low = df['Low'].values
                close = df['Close'].values
                adx = talib.ADX(high, low, close, timeperiod=period)
                return pd.Series(adx, index=df.index)
            except:
                pass

        # Fallback calculation - simplified ADX approximation
        high = df['High']
        low = df['Low']
        close = df['Close']

        # Calculate True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # Simplified ADX calculation
        atr = tr.rolling(window=period).mean()
        adx_approx = (atr / close * 100).rolling(window=period).mean()

        return adx_approx.fillna(25)
    
    def _generate_dummy_data(self, symbol: str, interval: str, period: str) -> pd.DataFrame:
        """Generate dummy data for testing"""
        # Determine number of periods
        if period == '1d':
            if interval == '15m':
                periods = 26  # 6.5 hours * 4 periods per hour
            elif interval == '5m':
                periods = 78  # 6.5 hours * 12 periods per hour
            else:
                periods = 26
        else:
            periods = 100
        
        # Generate timestamps
        end_time = datetime.now()
        if interval == '15m':
            freq = '15T'
        elif interval == '5m':
            freq = '5T'
        else:
            freq = '15T'
        
        timestamps = pd.date_range(end=end_time, periods=periods, freq=freq)
        
        # Generate realistic price data
        base_price = 2500  # Base price
        returns = np.random.normal(0, 0.02, periods)  # 2% volatility
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create OHLCV data
        data = []
        for i, (ts, price) in enumerate(zip(timestamps, prices)):
            high = price * (1 + abs(np.random.normal(0, 0.01)))
            low = price * (1 - abs(np.random.normal(0, 0.01)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                'Open': open_price,
                'High': max(open_price, high, close_price),
                'Low': min(open_price, low, close_price),
                'Close': close_price,
                'Volume': volume
            })
        
        df = pd.DataFrame(data, index=timestamps)
        return df
    
    def _generate_dummy_daily_data(self, symbol: str, period: str) -> pd.DataFrame:
        """Generate dummy daily data"""
        if period == '1y':
            periods = 252
        elif period == '6mo':
            periods = 126
        else:
            periods = 100
        
        end_date = datetime.now().date()
        dates = pd.date_range(end=end_date, periods=periods, freq='D')
        
        # Generate realistic daily price data
        base_price = 2500
        returns = np.random.normal(0, 0.02, periods)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            high = price * (1 + abs(np.random.normal(0, 0.02)))
            low = price * (1 - abs(np.random.normal(0, 0.02)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            volume = np.random.randint(1000000, 10000000)
            
            data.append({
                'Open': open_price,
                'High': max(open_price, high, close_price),
                'Low': min(open_price, low, close_price),
                'Close': close_price,
                'Volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def get_sector_performance(self) -> dict:
        """Get sector performance data"""
        # This would typically fetch from a real data source
        # For now, return mock data
        return {
            'sector_performance': {
                'NIFTY_BANK': 1.2,
                'NIFTY_IT': 0.8,
                'NIFTY_AUTO': -0.5,
                'NIFTY_PHARMA': 0.3,
                'NIFTY_METAL': 1.5,
                'NIFTY_ENERGY': 0.7,
                'NIFTY_FMCG': 0.2
            }
        }
