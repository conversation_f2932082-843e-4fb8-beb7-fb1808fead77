#!/usr/bin/env python3
"""
Cleanup Unused Files Script
Removes unused files after successful CrewAI installation
"""

import os
import shutil
from pathlib import Path

def cleanup_unused_files():
    """Remove unused files after CrewAI installation"""
    
    print("🧹 CLEANING UP UNUSED FILES")
    print("=" * 50)
    print("🎯 Removing files that are no longer needed after CrewAI conversion")
    
    # Files to remove (unused after CrewAI conversion)
    files_to_remove = [
        # Old agent files (if they exist from previous attempts)
        "main.py",  # Old CrewAI main (replaced by crewai_indian_analyzer.py)
        "agents/supervisor_agent.py",
        "agents/news_analysis_agent.py", 
        "agents/data_collection_agent.py",
        "agents/validation_agent.py",
        
        # Old strategy files
        "strategies/backtrader_strategies.py",
        "run_backtest.py",
        
        # Superseded tools
        "tools/enhanced_market_data.py",
        "tools/bloomberg_integration.py",
        
        # Redundant main scripts (keeping smart_market_analyzer.py as fallback)
        "main_enhanced.py",
        "main_indian_market.py"
    ]
    
    # Directories to remove
    directories_to_remove = [
        "agents/strategy_agents",  # Old strategy agents
        "strategies"  # Backtrader strategies
    ]
    
    removed_files = []
    removed_dirs = []
    
    # Remove individual files
    print("\n📄 Removing unused files...")
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"   ✅ Removed: {file_path}")
            except Exception as e:
                print(f"   ❌ Failed to remove {file_path}: {e}")
        else:
            print(f"   ⚪ Not found: {file_path}")
    
    # Remove directories
    print("\n📁 Removing unused directories...")
    for dir_path in directories_to_remove:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                removed_dirs.append(dir_path)
                print(f"   ✅ Removed directory: {dir_path}")
            except Exception as e:
                print(f"   ❌ Failed to remove {dir_path}: {e}")
        else:
            print(f"   ⚪ Not found: {dir_path}")
    
    # Remove empty agents directory if it exists and is empty
    if os.path.exists("agents") and not os.listdir("agents"):
        try:
            os.rmdir("agents")
            removed_dirs.append("agents")
            print(f"   ✅ Removed empty directory: agents")
        except Exception as e:
            print(f"   ❌ Failed to remove agents directory: {e}")
    
    return removed_files, removed_dirs

def display_remaining_files():
    """Display the clean file structure"""
    
    print(f"\n{'='*50}")
    print("📂 CLEAN PROJECT STRUCTURE")
    print(f"{'='*50}")
    
    essential_files = [
        "🚀 crewai_indian_analyzer.py",  # Main CrewAI system
        "🔄 smart_market_analyzer.py",   # Fallback system
        "📊 main_simple.py",             # Basic system
        "🧪 backtest_indian_strategies.py",  # Validation
        "🔧 install_crewai_system.py",   # Installation
        "🚀 launch_crewai.py",           # Launcher
        "🧹 cleanup_unused_files.py",    # This script
        "📋 final_validation_test.py",   # Testing
    ]
    
    essential_dirs = [
        "🤖 crewai_agents/",             # CrewAI agent tools
        "🛠️ tools/",                     # Core tools
        "⚙️ config/",                    # Configuration
        "📚 docs/ (documentation)",      # Documentation
    ]
    
    print("\n✅ ESSENTIAL FILES (KEPT):")
    for file_desc in essential_files:
        print(f"   {file_desc}")
    
    print("\n✅ ESSENTIAL DIRECTORIES (KEPT):")
    for dir_desc in essential_dirs:
        print(f"   {dir_desc}")

def verify_crewai_system():
    """Verify CrewAI system is still functional after cleanup"""
    
    print(f"\n{'='*50}")
    print("🧪 VERIFYING CREWAI SYSTEM")
    print(f"{'='*50}")
    
    try:
        # Test CrewAI imports
        import crewai
        print("✅ CrewAI core imported")
        
        # Test agent imports
        from crewai_agents.indian_data_agent import IndianDataAgent
        from crewai_agents.indian_news_agent import IndianNewsAgent
        from crewai_agents.technical_analysis_agent import TechnicalAnalysisAgent
        from crewai_agents.strategy_execution_agent import StrategyExecutionAgent
        from crewai_agents.market_timing_agent import MarketTimingAgent
        from crewai_agents.risk_management_agent import RiskManagementAgent
        from crewai_agents.report_generation_agent import ReportGenerationAgent
        
        print("✅ All CrewAI agents imported successfully")
        
        # Test fallback system
        from smart_market_analyzer import SmartMarketAnalyzer
        print("✅ Fallback system available")
        
        # Test core tools
        from tools.indian_market_data import IndianMarketData
        from tools.indian_news_aggregator import IndianNewsAggregator
        from tools.nlp_processor import NLPProcessor
        
        print("✅ Core tools available")
        
        print("\n🎉 SYSTEM VERIFICATION PASSED!")
        print("🤖 CrewAI system is fully functional")
        print("🔄 Fallback system is available")
        
        return True
        
    except Exception as e:
        print(f"❌ System verification failed: {e}")
        print("⚠️ Some components may not be working correctly")
        return False

def display_usage_instructions():
    """Display usage instructions for the clean system"""
    
    print(f"\n{'='*60}")
    print("🎯 USAGE INSTRUCTIONS")
    print(f"{'='*60}")
    
    print("\n🤖 CREWAI AGENTIC SYSTEM:")
    print("   🚀 python launch_crewai.py")
    print("   🎯 python crewai_indian_analyzer.py")
    
    print("\n🔄 FALLBACK SYSTEMS:")
    print("   📊 python smart_market_analyzer.py")
    print("   📈 python main_simple.py")
    
    print("\n🧪 TESTING & VALIDATION:")
    print("   ✅ python final_validation_test.py")
    print("   📊 python backtest_indian_strategies.py")
    
    print("\n⏰ OPTIMAL TIMING:")
    print("   🌅 8:45 AM IST - Pre-market preparation")
    print("   🚀 10:15 AM IST - Prime execution time")
    print("   📈 1:15 PM IST - Afternoon opportunities")
    
    print("\n🎯 FEATURES:")
    print("   ✅ 7 specialized AI agents")
    print("   ✅ Same output as original system")
    print("   ✅ Enhanced intelligence")
    print("   ✅ Automatic fallback")
    print("   ✅ Indian market focused")

def main():
    """Main cleanup function"""
    
    print("🇮🇳 CrewAI Indian Stock Market Analyzer")
    print("🧹 Post-Installation Cleanup")
    
    # Confirm cleanup
    print("\n⚠️ This will remove unused files from the project")
    print("📋 Files to be removed:")
    print("   • Old agent files")
    print("   • Superseded tools") 
    print("   • Redundant main scripts")
    print("   • Backtrader strategies")
    
    response = input("\n❓ Proceed with cleanup? (y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Perform cleanup
    removed_files, removed_dirs = cleanup_unused_files()
    
    # Display results
    print(f"\n{'='*50}")
    print("📊 CLEANUP SUMMARY")
    print(f"{'='*50}")
    print(f"📄 Files removed: {len(removed_files)}")
    print(f"📁 Directories removed: {len(removed_dirs)}")
    
    if removed_files:
        print("\n📄 Removed files:")
        for file in removed_files:
            print(f"   • {file}")
    
    if removed_dirs:
        print("\n📁 Removed directories:")
        for dir in removed_dirs:
            print(f"   • {dir}")
    
    # Show clean structure
    display_remaining_files()
    
    # Verify system
    if verify_crewai_system():
        print("\n✅ CLEANUP SUCCESSFUL!")
        print("🤖 CrewAI system is ready for use")
    else:
        print("\n⚠️ CLEANUP COMPLETED WITH WARNINGS")
        print("🔧 Some components may need attention")
    
    # Display usage instructions
    display_usage_instructions()
    
    print(f"\n🎉 Project cleanup complete!")
    print(f"🚀 Ready for agentic AI Indian stock market analysis!")

if __name__ == "__main__":
    main()
