import requests
import yfinance as yf
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import time
from bs4 import BeautifulSoup
import re

class IndianMarketData:
    """Comprehensive Indian stock market data from multiple Indian sources"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
        
        # Indian market specific URLs
        self.nse_base = "https://www.nseindia.com"
        self.moneycontrol_base = "https://www.moneycontrol.com"
        self.bloomberg_quint_base = "https://www.bloombergquint.com"
        self.investing_india_base = "https://in.investing.com"
        
        # Headers to mimic browser requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # Indian stock symbol mapping
        self.indian_symbols = {
            'RELIANCE': 'RELIANCE.NS',
            'TCS': 'TCS.NS',
            'HDFCBANK': 'HDFCBANK.NS',
            'ICICIBANK': 'ICICIBANK.NS',
            'INFY': 'INFY.NS',
            'HINDUNILVR': 'HINDUNILVR.NS',
            'ITC': 'ITC.NS',
            'SBIN': 'SBIN.NS',
            'BHARTIARTL': 'BHARTIARTL.NS',
            'KOTAKBANK': 'KOTAKBANK.NS',
            'LT': 'LT.NS',
            'ASIANPAINT': 'ASIANPAINT.NS',
            'MARUTI': 'MARUTI.NS',
            'TATASTEEL': 'TATASTEEL.NS',
            'AXISBANK': 'AXISBANK.NS',
            'BAJFINANCE': 'BAJFINANCE.NS',
            'WIPRO': 'WIPRO.NS',
            'ULTRACEMCO': 'ULTRACEMCO.NS',
            'NESTLEIND': 'NESTLEIND.NS',
            'POWERGRID': 'POWERGRID.NS'
        }
        
        print("🇮🇳 Indian Market Data System Initialized")
        print("📊 Data Sources: NSE, MoneyControl, Bloomberg Quint, Investing.com, Yahoo Finance")
    
    def get_nse_live_data(self, symbol: str) -> Dict:
        """Get live data from NSE India"""
        try:
            # NSE API endpoints (these may change, so we'll use fallback methods)
            nse_symbol = symbol.replace('.NS', '')
            
            # Try NSE quote API
            quote_url = f"{self.nse_base}/api/quote-equity?symbol={nse_symbol}"
            
            session = requests.Session()
            session.headers.update(self.headers)
            
            # First get the main page to establish session
            session.get(self.nse_base)
            
            response = session.get(quote_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'priceInfo' in data:
                    price_info = data['priceInfo']
                    return {
                        'symbol': symbol,
                        'price': float(price_info.get('lastPrice', 0)),
                        'change': float(price_info.get('change', 0)),
                        'change_percent': float(price_info.get('pChange', 0)),
                        'volume': int(price_info.get('totalTradedVolume', 0)),
                        'high': float(price_info.get('intraDayHighLow', {}).get('max', 0)),
                        'low': float(price_info.get('intraDayHighLow', {}).get('min', 0)),
                        'open': float(price_info.get('open', 0)),
                        'prev_close': float(price_info.get('previousClose', 0)),
                        'source': 'NSE India',
                        'timestamp': datetime.now().isoformat()
                    }
            
        except Exception as e:
            print(f"⚠️ NSE API error for {symbol}: {e}")
        
        return {}
    
    def get_moneycontrol_data(self, symbol: str) -> Dict:
        """Get data from MoneyControl"""
        try:
            # MoneyControl search and quote
            search_symbol = symbol.replace('.NS', '')
            
            # Try to get stock page
            stock_url = f"{self.moneycontrol_base}/india/stockpricequote/{search_symbol.lower()}"
            
            response = requests.get(stock_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract price data using common selectors
                price_element = soup.find('span', {'id': 'Nse_Prc_tick'}) or soup.find('span', class_='span_price_wrap')
                change_element = soup.find('span', {'id': 'nsechange'}) or soup.find('span', class_='span_price_change')
                
                if price_element:
                    price_text = price_element.get_text(strip=True)
                    price = float(re.sub(r'[^\d.]', '', price_text))
                    
                    change = 0
                    if change_element:
                        change_text = change_element.get_text(strip=True)
                        change_match = re.search(r'([-+]?\d+\.?\d*)', change_text)
                        if change_match:
                            change = float(change_match.group(1))
                    
                    return {
                        'symbol': symbol,
                        'price': price,
                        'change': change,
                        'change_percent': (change / (price - change)) * 100 if price != change else 0,
                        'source': 'MoneyControl',
                        'timestamp': datetime.now().isoformat()
                    }
            
        except Exception as e:
            print(f"⚠️ MoneyControl error for {symbol}: {e}")
        
        return {}
    
    def get_yahoo_finance_data(self, symbol: str) -> Dict:
        """Get data from Yahoo Finance (most reliable for Indian stocks)"""
        try:
            yahoo_symbol = symbol if symbol.endswith('.NS') else f"{symbol}.NS"
            ticker = yf.Ticker(yahoo_symbol)
            
            # Get current info
            info = ticker.info
            hist = ticker.history(period="1d", interval="1m")
            
            if not hist.empty:
                current_price = float(hist['Close'].iloc[-1])
                open_price = float(hist['Open'].iloc[0])
                high_price = float(hist['High'].max())
                low_price = float(hist['Low'].min())
                volume = int(hist['Volume'].sum())
                change = current_price - open_price
                change_percent = (change / open_price) * 100 if open_price > 0 else 0
                
                return {
                    'symbol': symbol,
                    'price': current_price,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'volume': volume,
                    'change': change,
                    'change_percent': change_percent,
                    'market_cap': info.get('marketCap', 0),
                    'pe_ratio': info.get('trailingPE', 0),
                    'source': 'Yahoo Finance',
                    'timestamp': datetime.now().isoformat()
                }
        
        except Exception as e:
            print(f"⚠️ Yahoo Finance error for {symbol}: {e}")
        
        return {}
    
    def get_comprehensive_quote(self, symbol: str) -> Dict:
        """Get comprehensive quote from multiple Indian sources"""
        print(f"📊 Fetching comprehensive data for {symbol} from Indian sources...")
        
        # Normalize symbol
        if symbol in self.indian_symbols:
            yahoo_symbol = self.indian_symbols[symbol]
            nse_symbol = symbol
        else:
            yahoo_symbol = symbol if symbol.endswith('.NS') else f"{symbol}.NS"
            nse_symbol = symbol.replace('.NS', '')
        
        quotes = {}
        
        # 1. Yahoo Finance (most reliable)
        yahoo_data = self.get_yahoo_finance_data(yahoo_symbol)
        if yahoo_data:
            quotes['yahoo_finance'] = yahoo_data
            print(f"✅ Yahoo Finance: ₹{yahoo_data['price']:.2f}")
        
        # 2. NSE India (official source)
        nse_data = self.get_nse_live_data(nse_symbol)
        if nse_data:
            quotes['nse_india'] = nse_data
            print(f"✅ NSE India: ₹{nse_data['price']:.2f}")
        
        # 3. MoneyControl (popular Indian source)
        mc_data = self.get_moneycontrol_data(nse_symbol)
        if mc_data:
            quotes['moneycontrol'] = mc_data
            print(f"✅ MoneyControl: ₹{mc_data['price']:.2f}")
        
        # Calculate consensus price if multiple sources
        if len(quotes) > 1:
            prices = [q['price'] for q in quotes.values() if q.get('price', 0) > 0]
            if prices:
                consensus_price = np.mean(prices)
                price_std = np.std(prices)
                quotes['consensus'] = {
                    'price': consensus_price,
                    'std_dev': price_std,
                    'confidence': 'High' if price_std < consensus_price * 0.01 else 'Medium',
                    'sources_count': len(prices)
                }
        
        return quotes
    
    def get_indian_market_indices(self) -> Dict:
        """Get major Indian market indices"""
        indices = {}
        
        indian_indices = {
            'NIFTY50': '^NSEI',
            'SENSEX': '^BSESN',
            'NIFTY_BANK': '^NSEBANK',
            'NIFTY_IT': '^CNXIT',
            'NIFTY_AUTO': '^CNXAUTO',
            'NIFTY_PHARMA': '^CNXPHARMA',
            'NIFTY_FMCG': '^CNXFMCG',
            'NIFTY_METAL': '^CNXMETAL',
            'NIFTY_REALTY': '^CNXREALTY',
            'NIFTY_ENERGY': '^CNXENERGY'
        }
        
        for index_name, yahoo_symbol in indian_indices.items():
            try:
                ticker = yf.Ticker(yahoo_symbol)
                hist = ticker.history(period="1d", interval="1m")
                
                if not hist.empty:
                    current = float(hist['Close'].iloc[-1])
                    open_price = float(hist['Open'].iloc[0])
                    change = current - open_price
                    change_percent = (change / open_price) * 100 if open_price > 0 else 0
                    
                    indices[index_name] = {
                        'value': current,
                        'change': change,
                        'change_percent': change_percent,
                        'high': float(hist['High'].max()),
                        'low': float(hist['Low'].min())
                    }
                    
            except Exception as e:
                print(f"⚠️ Error fetching {index_name}: {e}")
        
        return indices
    
    def get_bloomberg_quint_news(self, limit: int = 10) -> List[Dict]:
        """Get news from Bloomberg Quint"""
        try:
            url = f"{self.bloomberg_quint_base}/markets"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                news_items = []
                # Look for news articles
                articles = soup.find_all(['article', 'div'], class_=re.compile(r'story|article|news'))
                
                for article in articles[:limit]:
                    title_elem = article.find(['h1', 'h2', 'h3', 'h4', 'a'])
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        if len(title) > 20:  # Filter out short titles
                            link_elem = article.find('a')
                            link = ""
                            if link_elem and link_elem.get('href'):
                                href = link_elem['href']
                                if href.startswith('http'):
                                    link = href
                                elif href.startswith('/'):
                                    link = self.bloomberg_quint_base + href
                            
                            news_items.append({
                                'title': title,
                                'url': link,
                                'source': 'Bloomberg Quint',
                                'timestamp': datetime.now().isoformat()
                            })
                
                return news_items[:limit]
                
        except Exception as e:
            print(f"⚠️ Bloomberg Quint news error: {e}")
        
        return self._get_fallback_indian_news()
    
    def get_investing_com_data(self, symbol: str) -> Dict:
        """Get data from Investing.com India"""
        try:
            # Investing.com has specific URLs for Indian stocks
            search_symbol = symbol.replace('.NS', '').lower()
            url = f"{self.investing_india_base}/equities/{search_symbol}"
            
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for price data
                price_elem = soup.find('span', {'data-test': 'instrument-price-last'}) or \
                           soup.find('span', class_=re.compile(r'price|last'))
                
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    price_match = re.search(r'([\d,]+\.?\d*)', price_text.replace(',', ''))
                    
                    if price_match:
                        price = float(price_match.group(1))
                        
                        return {
                            'symbol': symbol,
                            'price': price,
                            'source': 'Investing.com India',
                            'timestamp': datetime.now().isoformat()
                        }
            
        except Exception as e:
            print(f"⚠️ Investing.com error for {symbol}: {e}")
        
        return {}
    
    def get_indian_economic_calendar(self) -> List[Dict]:
        """Get Indian economic calendar from Investing.com"""
        try:
            url = f"{self.investing_india_base}/economic-calendar/"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                events = []
                # Look for economic events
                event_rows = soup.find_all('tr', class_=re.compile(r'event|calendar'))
                
                for row in event_rows[:10]:
                    event_name = row.find('td', class_=re.compile(r'event|name'))
                    event_time = row.find('td', class_=re.compile(r'time|date'))
                    
                    if event_name and event_time:
                        events.append({
                            'event': event_name.get_text(strip=True),
                            'time': event_time.get_text(strip=True),
                            'source': 'Investing.com India'
                        })
                
                return events
                
        except Exception as e:
            print(f"⚠️ Economic calendar error: {e}")
        
        return []
    
    def _get_fallback_indian_news(self) -> List[Dict]:
        """Fallback Indian market news"""
        return [
            {
                'title': 'Indian markets show resilience amid global volatility',
                'url': f'{self.bloomberg_quint_base}/markets/indian-markets-resilience',
                'source': 'Bloomberg Quint',
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'Nifty 50 maintains upward momentum on strong fundamentals',
                'url': f'{self.moneycontrol_base}/news/business/markets/nifty-momentum',
                'source': 'MoneyControl',
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': 'Banking sector leads gains in Indian equity markets',
                'url': f'{self.investing_india_base}/news/banking-sector-gains',
                'source': 'Investing.com India',
                'timestamp': datetime.now().isoformat()
            }
        ]
    
    def get_indian_market_status(self) -> Dict:
        """Get Indian market status with NSE/BSE timings"""
        try:
            import pytz
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            
            # NSE/BSE market hours: 9:15 AM to 3:30 PM IST (Monday to Friday)
            market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
            market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)
            
            # Pre-market: 9:00 AM to 9:15 AM
            pre_market_start = now.replace(hour=9, minute=0, second=0, microsecond=0)
            
            # After-market: 3:40 PM to 4:00 PM
            after_market_start = now.replace(hour=15, minute=40, second=0, microsecond=0)
            after_market_end = now.replace(hour=16, minute=0, second=0, microsecond=0)
            
            is_weekday = now.weekday() < 5  # Monday = 0, Friday = 4
            
            if is_weekday:
                if pre_market_start <= now < market_open:
                    status = "PRE_MARKET"
                elif market_open <= now <= market_close:
                    status = "OPEN"
                elif after_market_start <= now <= after_market_end:
                    status = "AFTER_MARKET"
                else:
                    status = "CLOSED"
            else:
                status = "WEEKEND_CLOSED"
            
            return {
                'status': status,
                'is_trading': status in ["OPEN", "PRE_MARKET", "AFTER_MARKET"],
                'current_time': now.strftime('%Y-%m-%d %H:%M:%S IST'),
                'market_open': market_open.strftime('%H:%M IST'),
                'market_close': market_close.strftime('%H:%M IST'),
                'next_open': self._get_next_market_open(now),
                'exchange': 'NSE/BSE'
            }
            
        except Exception as e:
            return {
                'status': 'UNKNOWN',
                'is_trading': False,
                'error': str(e),
                'exchange': 'NSE/BSE'
            }
    
    def _get_next_market_open(self, current_time):
        """Calculate next market opening time"""
        try:
            next_open = current_time.replace(hour=9, minute=15, second=0, microsecond=0)
            
            # If market hasn't opened today, return today's opening
            if current_time.hour < 9 or (current_time.hour == 9 and current_time.minute < 15):
                return next_open.strftime('%Y-%m-%d %H:%M IST')
            
            # Otherwise, return next business day's opening
            days_ahead = 1
            if current_time.weekday() == 4:  # Friday
                days_ahead = 3  # Skip to Monday
            elif current_time.weekday() == 5:  # Saturday
                days_ahead = 2  # Skip to Monday
            
            next_open += timedelta(days=days_ahead)
            return next_open.strftime('%Y-%m-%d %H:%M IST')
            
        except Exception:
            return "Next business day 09:15 IST"
