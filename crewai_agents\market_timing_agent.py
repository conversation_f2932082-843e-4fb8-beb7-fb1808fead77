"""
Market Timing Agent for CrewAI
Specialized agent for optimal Indian market timing
"""

from crewai_tools import BaseTool
from typing import Dict, Any
from datetime import datetime
import pytz

class MarketTimingAgent(BaseTool):
    name: str = "Indian Market Timing Expert"
    description: str = """
    Determines optimal execution times for Indian market trading.
    Knows that 8:45 AM IST is best for pre-market analysis, 10:15 AM IST 
    is the golden hour for execution, and understands NSE/BSE trading patterns.
    """
    
    def _run(self, market_status: str = "", current_time: str = "") -> Dict[str, Any]:
        """Determine optimal timing for Indian market execution"""
        
        try:
            print(f"⏰ Analyzing optimal timing for Indian market...")
            
            # Get current IST time
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time_str = now.strftime("%H:%M")
            
            # Determine market status
            is_weekday = now.weekday() < 5
            
            if not is_weekday:
                status = 'WEEKEND'
                is_live = False
                mode = 'PREDICTIVE'
            elif current_time_str < "09:15":
                status = 'PRE_MARKET'
                is_live = False
                mode = 'PREDICTIVE'
            elif "09:15" <= current_time_str <= "15:30":
                status = 'LIVE_MARKET'
                is_live = True
                mode = 'LIVE'
            else:
                status = 'POST_MARKET'
                is_live = False
                mode = 'ANALYSIS'
            
            # Determine urgency and optimal timing
            timing_analysis = self._analyze_timing(current_time_str, status, is_live)
            
            timing_result = {
                'current_time': now.strftime('%Y-%m-%d %H:%M:%S IST'),
                'market_status': status,
                'is_live_market': is_live,
                'analysis_mode': mode,
                'urgency_level': timing_analysis['urgency'],
                'optimal_execution_window': timing_analysis['execution_window'],
                'next_optimal_time': timing_analysis['next_optimal'],
                'timing_recommendation': timing_analysis['recommendation'],
                'execution_priority': timing_analysis['priority'],
                'indian_market_insights': self._get_indian_market_insights(current_time_str, status)
            }
            
            print(f"   🎯 Status: {status} | Urgency: {timing_analysis['urgency']}")
            print(f"   ⏰ Next Optimal: {timing_analysis['next_optimal']}")
            
            return timing_result
            
        except Exception as e:
            print(f"❌ Error in timing analysis: {e}")
            return {
                'error': str(e),
                'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_status': 'ERROR',
                'urgency_level': 'LOW'
            }
    
    def _analyze_timing(self, current_time: str, status: str, is_live: bool) -> Dict[str, Any]:
        """Analyze current timing for optimal execution"""
        
        # Optimal times for Indian market
        optimal_times = {
            "08:45": "Pre-market preparation (CRITICAL)",
            "09:25": "First live check (opening analysis)",
            "10:15": "GOLDEN HOUR (best execution)",
            "11:00": "Mid-morning opportunities",
            "13:15": "Post-lunch session",
            "14:45": "Pre-closing analysis",
            "15:20": "Final opportunities"
        }
        
        if is_live:
            # Live market timing analysis
            if current_time >= "09:15" and current_time <= "09:30":
                urgency = "MEDIUM"
                execution_window = "15 minutes"
                recommendation = "Wait for market to settle, prepare for 10:15 AM execution"
                priority = "PREPARE"
                next_optimal = "10:15 AM IST"
                
            elif current_time >= "10:00" and current_time <= "10:30":
                urgency = "HIGH"
                execution_window = "30 minutes"
                recommendation = "PRIME EXECUTION TIME - Execute high-confidence signals immediately"
                priority = "EXECUTE"
                next_optimal = "Current time is optimal"
                
            elif current_time >= "11:00" and current_time <= "11:30":
                urgency = "MEDIUM"
                execution_window = "30 minutes"
                recommendation = "Good time for swing trading entries and momentum plays"
                priority = "SELECTIVE"
                next_optimal = "13:15 PM IST"
                
            elif current_time >= "13:15" and current_time <= "13:45":
                urgency = "MEDIUM"
                execution_window = "30 minutes"
                recommendation = "Post-lunch session - fresh momentum opportunities"
                priority = "SELECTIVE"
                next_optimal = "14:45 PM IST"
                
            elif current_time >= "14:45" and current_time <= "15:15":
                urgency = "HIGH"
                execution_window = "30 minutes"
                recommendation = "Pre-closing hour - final trading opportunities"
                priority = "FINAL_CALL"
                next_optimal = "15:20 PM IST"
                
            elif current_time >= "15:20" and current_time <= "15:30":
                urgency = "CRITICAL"
                execution_window = "10 minutes"
                recommendation = "FINAL MINUTES - Only execute if absolutely necessary"
                priority = "EMERGENCY"
                next_optimal = "Market closing soon"
                
            else:
                urgency = "LOW"
                execution_window = "Monitor"
                recommendation = "Non-optimal time - wait for next window"
                priority = "WAIT"
                next_optimal = self._get_next_optimal_time(current_time, optimal_times)
        
        else:
            # Market closed timing analysis
            if status == 'PRE_MARKET':
                urgency = "HIGH"
                execution_window = "Until market open"
                recommendation = "CRITICAL PRE-MARKET PREPARATION TIME"
                priority = "PREPARE"
                next_optimal = "Market opens at 09:15 AM IST"
                
            elif status == 'POST_MARKET':
                urgency = "LOW"
                execution_window = "Analysis mode"
                recommendation = "Analyze today's performance and prepare for tomorrow"
                priority = "REVIEW"
                next_optimal = "08:45 AM IST tomorrow"
                
            else:  # Weekend
                urgency = "LOW"
                execution_window = "Predictive analysis"
                recommendation = "Weekend analysis and next week preparation"
                priority = "PLAN"
                next_optimal = "Monday 08:45 AM IST"
        
        return {
            'urgency': urgency,
            'execution_window': execution_window,
            'recommendation': recommendation,
            'priority': priority,
            'next_optimal': next_optimal
        }
    
    def _get_next_optimal_time(self, current_time: str, optimal_times: Dict) -> str:
        """Get next optimal time"""
        
        current_minutes = int(current_time.split(':')[0]) * 60 + int(current_time.split(':')[1])
        
        for time_str in optimal_times.keys():
            time_minutes = int(time_str.split(':')[0]) * 60 + int(time_str.split(':')[1])
            if time_minutes > current_minutes:
                return f"{time_str} IST"
        
        return "Next trading day 08:45 AM IST"
    
    def _get_indian_market_insights(self, current_time: str, status: str) -> Dict[str, Any]:
        """Get Indian market specific timing insights"""
        
        insights = {
            'market_characteristics': {
                'opening_volatility': "First 10 minutes (9:15-9:25) are highly volatile",
                'golden_hour': "10:15-10:45 AM provides most reliable signals",
                'lunch_impact': "12:00-13:00 PM typically sees reduced activity",
                'closing_rush': "Last 30 minutes can be volatile"
            },
            'optimal_strategies_by_time': {},
            'risk_factors': [],
            'execution_tips': []
        }
        
        if status == 'LIVE_MARKET':
            if "09:15" <= current_time <= "09:30":
                insights['optimal_strategies_by_time'] = {
                    'primary': 'Gap analysis and opening range breakouts',
                    'secondary': 'Wait for volatility to settle',
                    'avoid': 'Large position sizes due to volatility'
                }
                insights['risk_factors'] = ['High volatility', 'Wide spreads', 'Emotional trading']
                insights['execution_tips'] = ['Use smaller position sizes', 'Wait for clear signals', 'Avoid FOMO']
                
            elif "10:00" <= current_time <= "10:45":
                insights['optimal_strategies_by_time'] = {
                    'primary': 'Mean Reversion and MACD Crossover',
                    'secondary': 'Momentum breakouts with volume',
                    'focus': 'High-confidence signals only'
                }
                insights['risk_factors'] = ['Market direction uncertainty']
                insights['execution_tips'] = ['Execute proven strategies', 'Use full position sizes', 'Set proper stop-losses']
                
            elif "13:15" <= current_time <= "14:00":
                insights['optimal_strategies_by_time'] = {
                    'primary': 'Afternoon momentum plays',
                    'secondary': 'Sector rotation opportunities',
                    'focus': 'Fresh breakouts and reversals'
                }
                insights['risk_factors'] = ['Reduced liquidity during lunch']
                insights['execution_tips'] = ['Monitor volume carefully', 'Focus on liquid stocks', 'Quick execution']
                
            elif "14:45" <= current_time <= "15:30":
                insights['optimal_strategies_by_time'] = {
                    'primary': 'Final hour momentum',
                    'secondary': 'Position squaring opportunities',
                    'focus': 'Day trading exits and overnight decisions'
                }
                insights['risk_factors'] = ['Closing volatility', 'Time pressure']
                insights['execution_tips'] = ['Square off day trades', 'Plan overnight positions', 'Avoid new large positions']
        
        else:
            insights['optimal_strategies_by_time'] = {
                'primary': 'Predictive analysis and preparation',
                'secondary': 'News impact assessment',
                'focus': 'Tomorrow\'s watchlist and key levels'
            }
            insights['execution_tips'] = ['Prepare trading plan', 'Identify key levels', 'Monitor global cues']
        
        return insights
