import requests
from bs4 import BeautifulSoup
from datetime import datetime
import re
import time

class MoneyControlScraper:
    def __init__(self):
        self.base_url = "https://www.moneycontrol.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def get_top_news(self, sector=None):
        """Get top news from Moneycontrol"""
        if sector:
            url = f"{self.base_url}/news/{sector}/"
        else:
            url = f"{self.base_url}/news/"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            news_items = []
            
            # Try different selectors for news items
            selectors = [
                '.clearfix.newslist',
                '.news_common',
                '.news-item',
                'article',
                '.news-box'
            ]
            
            for selector in selectors:
                items = soup.select(selector)
                if items:
                    break
            
            if not items:
                # Fallback to generic approach
                items = soup.find_all(['div', 'article'], class_=re.compile(r'news|article'))
            
            for item in items[:10]:  # Limit to 10 items
                title_element = item.find(['h1', 'h2', 'h3', 'h4', 'a'])
                if not title_element:
                    continue
                
                title = title_element.get_text(strip=True)
                if not title or len(title) < 10:  # Skip very short titles
                    continue
                
                link_element = item.find('a')
                link = ""
                if link_element and link_element.get('href'):
                    href = link_element['href']
                    if href.startswith('http'):
                        link = href
                    elif href.startswith('/'):
                        link = self.base_url + href
                
                time_element = item.find(class_=re.compile(r'time|date|datetime'))
                time_text = time_element.get_text(strip=True) if time_element else ""
                
                news_items.append({
                    'title': title,
                    'link': link,
                    'time': time_text,
                    'sector': sector or 'general',
                    'source': 'Moneycontrol'
                })
            
            # If no news found, return dummy news
            if not news_items:
                news_items = self._get_dummy_news(sector)
            
            return news_items[:10]
            
        except Exception as e:
            print(f"Error fetching Moneycontrol news: {e}")
            return self._get_dummy_news(sector)
    
    def get_stock_news(self, symbol):
        """Get news specific to a stock"""
        try:
            # Try to search for stock-specific news
            search_url = f"{self.base_url}/news/search/?query={symbol}"
            response = requests.get(search_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                news_items = []
                
                # Look for news items
                items = soup.find_all(['div', 'article'], limit=5)
                
                for item in items:
                    title_element = item.find(['h1', 'h2', 'h3', 'a'])
                    if title_element and symbol.upper() in title_element.get_text().upper():
                        title = title_element.get_text(strip=True)
                        
                        link_element = item.find('a')
                        link = ""
                        if link_element and link_element.get('href'):
                            href = link_element['href']
                            if href.startswith('http'):
                                link = href
                            elif href.startswith('/'):
                                link = self.base_url + href
                        
                        news_items.append({
                            'title': title,
                            'link': link,
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M'),
                            'source': 'Moneycontrol'
                        })
                
                return news_items[:5]
            
        except Exception as e:
            print(f"Error fetching stock news for {symbol}: {e}")
        
        # Return dummy stock-specific news
        return self._get_dummy_stock_news(symbol)
    
    def _get_dummy_news(self, sector=None):
        """Generate dummy news for testing"""
        sector_name = sector or "Market"
        
        dummy_news = [
            {
                'title': f'{sector_name} shows strong momentum amid positive global cues',
                'link': f'{self.base_url}/news/dummy1',
                'time': '2 hours ago',
                'sector': sector or 'general',
                'source': 'Moneycontrol'
            },
            {
                'title': f'Analysts bullish on {sector_name} stocks for next quarter',
                'link': f'{self.base_url}/news/dummy2',
                'time': '4 hours ago',
                'sector': sector or 'general',
                'source': 'Moneycontrol'
            },
            {
                'title': f'{sector_name} sector witnesses increased institutional buying',
                'link': f'{self.base_url}/news/dummy3',
                'time': '6 hours ago',
                'sector': sector or 'general',
                'source': 'Moneycontrol'
            },
            {
                'title': f'FII activity in {sector_name} remains positive this week',
                'link': f'{self.base_url}/news/dummy4',
                'time': '8 hours ago',
                'sector': sector or 'general',
                'source': 'Moneycontrol'
            },
            {
                'title': f'{sector_name} companies report better than expected earnings',
                'link': f'{self.base_url}/news/dummy5',
                'time': '10 hours ago',
                'sector': sector or 'general',
                'source': 'Moneycontrol'
            }
        ]
        
        return dummy_news
    
    def _get_dummy_stock_news(self, symbol):
        """Generate dummy stock-specific news"""
        return [
            {
                'title': f'{symbol} reports strong quarterly results, beats estimates',
                'link': f'{self.base_url}/news/{symbol.lower()}-results',
                'time': '1 hour ago',
                'source': 'Moneycontrol'
            },
            {
                'title': f'Brokerages upgrade {symbol} target price on robust outlook',
                'link': f'{self.base_url}/news/{symbol.lower()}-upgrade',
                'time': '3 hours ago',
                'source': 'Moneycontrol'
            },
            {
                'title': f'{symbol} announces major expansion plans, stock rallies',
                'link': f'{self.base_url}/news/{symbol.lower()}-expansion',
                'time': '5 hours ago',
                'source': 'Moneycontrol'
            }
        ]
