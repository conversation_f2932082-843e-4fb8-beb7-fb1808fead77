#!/usr/bin/env python3
"""
Nifty Stocks Fetcher
Fetches all stocks from Nifty indices that are enabled for intraday trading
"""

import requests
import pandas as pd
from typing import List, Dict
import json
import time

class NiftyStocksFetcher:
    """Fetches stocks from various Nifty indices"""
    
    def __init__(self):
        self.nse_base_url = "https://www.nseindia.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # Predefined lists as fallback
        self.nifty_50_stocks = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK',
            'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH',
            'BAJFINANCE', 'POWERGRID', 'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA',
            'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJFINSV', 'BAJAJ-AUTO',
            'BRITANNIA', 'COALINDIA', 'DIVISLAB', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS'
        ]
        
        self.nifty_100_additional = [
            'GODREJCP', 'PIDILITIND', 'DABUR', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'AMBUJACEM', 'ACC', 'SHREECEM', 'RAMCOCEM',
            'VEDL', 'SAIL', 'NMDC', 'MOIL', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK',
            'BANDHANBNK', 'AUBANK', 'CHOLAFIN', 'MUTHOOTFIN', 'PFC', 'RECLTD', 'IRCTC', 'CONCOR', 'GMRINFRA', 'ADANIGREEN',
            'TATAPOWER', 'TORNTPOWER', 'NHPC', 'SJVN', 'THERMAX', 'BHEL', 'BEL', 'HAL', 'BEML', 'RVNL'
        ]
        
        self.nifty_500_sample = [
            'ZEEL', 'STAR', 'SUNTV', 'NETWORK18', 'TV18BRDCST', 'DELTACORP', 'PVR', 'INOXLEISUR', 'JUBLFOOD', 'WESTLIFE',
            'TRENT', 'ADITYANB', 'ABFRL', 'RAYMOND', 'VMART', 'SHOPERSTOP', 'RELAXO', 'BATA', 'CROMPTON', 'HAVELLS',
            'VOLTAS', 'BLUESTARCO', 'WHIRLPOOL', 'AMBER', 'DIXON', 'ROUTE', 'LALPATHLAB', 'METROPOLIS', 'THYROCARE', 'KRBL',
            'LTF', 'DCMSHRIRAM', 'CHAMBLFERT', 'COROMANDEL', 'GNFC', 'NFL', 'RCF', 'FACT', 'GSFC', 'MADRASFERT'
        ]
    
    def get_nifty_50_stocks(self) -> List[str]:
        """Get Nifty 50 stocks"""
        try:
            print("📊 Fetching Nifty 50 stocks...")
            
            # Try to fetch from NSE API
            url = f"{self.nse_base_url}/api/equity-stockIndices?index=NIFTY%2050"
            
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                stocks = []
                
                if 'data' in data:
                    for item in data['data']:
                        symbol = item.get('symbol', '').replace('-', '_')
                        if symbol and symbol != 'NIFTY 50':
                            stocks.append(symbol)
                
                if stocks:
                    print(f"   ✅ Fetched {len(stocks)} stocks from NSE API")
                    return stocks[:50]  # Limit to 50
            
            print("   ⚠️ NSE API failed, using predefined Nifty 50 list")
            return self.nifty_50_stocks
            
        except Exception as e:
            print(f"   ❌ Error fetching Nifty 50: {e}")
            print("   🔄 Using predefined Nifty 50 list")
            return self.nifty_50_stocks
    
    def get_nifty_100_stocks(self) -> List[str]:
        """Get Nifty 100 stocks"""
        try:
            print("📊 Fetching Nifty 100 stocks...")
            
            # Get Nifty 50 first
            nifty_50 = self.get_nifty_50_stocks()
            
            # Try to fetch Nifty Next 50
            url = f"{self.nse_base_url}/api/equity-stockIndices?index=NIFTY%20NEXT%2050"
            
            response = requests.get(url, headers=self.headers, timeout=10)
            
            next_50 = []
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    for item in data['data']:
                        symbol = item.get('symbol', '').replace('-', '_')
                        if symbol and symbol != 'NIFTY NEXT 50':
                            next_50.append(symbol)
            
            if next_50:
                combined = nifty_50 + next_50
                print(f"   ✅ Combined Nifty 100: {len(combined)} stocks")
                return combined[:100]  # Limit to 100
            else:
                # Fallback to predefined list
                combined = self.nifty_50_stocks + self.nifty_100_additional
                print(f"   ⚠️ Using predefined Nifty 100 list: {len(combined)} stocks")
                return combined[:100]
            
        except Exception as e:
            print(f"   ❌ Error fetching Nifty 100: {e}")
            combined = self.nifty_50_stocks + self.nifty_100_additional
            return combined[:100]
    
    def get_nifty_500_stocks(self) -> List[str]:
        """Get Nifty 500 stocks (sample)"""
        try:
            print("📊 Fetching Nifty 500 stocks...")
            
            # Get Nifty 100 first
            nifty_100 = self.get_nifty_100_stocks()
            
            # Add more stocks for Nifty 500 simulation
            combined = nifty_100 + self.nifty_500_sample
            
            # Remove duplicates while preserving order
            seen = set()
            unique_stocks = []
            for stock in combined:
                if stock not in seen:
                    seen.add(stock)
                    unique_stocks.append(stock)
            
            print(f"   ✅ Nifty 500 sample: {len(unique_stocks)} stocks")
            return unique_stocks[:200]  # Limit to 200 for performance
            
        except Exception as e:
            print(f"   ❌ Error creating Nifty 500 list: {e}")
            return self.nifty_50_stocks
    
    def get_intraday_enabled_stocks(self, index_type: str = "nifty_100") -> List[str]:
        """Get stocks enabled for intraday trading"""
        
        print(f"🎯 Getting intraday-enabled stocks from {index_type.upper()}...")
        
        if index_type.lower() == "nifty_50":
            stocks = self.get_nifty_50_stocks()
        elif index_type.lower() == "nifty_100":
            stocks = self.get_nifty_100_stocks()
        elif index_type.lower() == "nifty_500":
            stocks = self.get_nifty_500_stocks()
        else:
            print(f"   ⚠️ Unknown index type: {index_type}, defaulting to Nifty 100")
            stocks = self.get_nifty_100_stocks()
        
        # Filter for intraday-enabled stocks (all major stocks are typically enabled)
        intraday_enabled = self._filter_intraday_enabled(stocks)
        
        print(f"✅ Found {len(intraday_enabled)} intraday-enabled stocks")
        return intraday_enabled
    
    def _filter_intraday_enabled(self, stocks: List[str]) -> List[str]:
        """Filter stocks that are enabled for intraday trading"""
        
        # Most liquid stocks are enabled for intraday
        # Exclude penny stocks and illiquid stocks
        excluded_patterns = [
            'SUZLON', 'YESBANK', 'RPOWER', 'JETAIRWAYS', 'DHFL', 'RELCAPITAL',
            'PCJEWELLER', 'JPASSOCIAT', 'JAIPRAKASH', 'JPPOWER', 'RCOM'
        ]
        
        filtered_stocks = []
        for stock in stocks:
            # Exclude known problematic stocks
            if not any(pattern in stock.upper() for pattern in excluded_patterns):
                filtered_stocks.append(stock)
        
        return filtered_stocks
    
    def get_top_liquid_stocks(self, count: int = 50) -> List[str]:
        """Get top liquid stocks for intraday trading"""
        
        print(f"💧 Getting top {count} liquid stocks for intraday...")
        
        # Top liquid stocks based on volume and market cap
        top_liquid = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK',
            'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH',
            'BAJFINANCE', 'POWERGRID', 'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA',
            'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJFINSV', 'BAJAJ-AUTO',
            'BRITANNIA', 'COALINDIA', 'DIVISLAB', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS',
            'GODREJCP', 'PIDILITIND', 'DABUR', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'AMBUJACEM', 'ACC', 'SHREECEM', 'RAMCOCEM',
            'VEDL', 'SAIL', 'NMDC', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK', 'BANDHANBNK',
            'AUBANK', 'CHOLAFIN', 'MUTHOOTFIN', 'PFC', 'RECLTD', 'IRCTC', 'CONCOR', 'GMRINFRA', 'ADANIGREEN', 'TATAPOWER',
            'TORNTPOWER', 'NHPC', 'SJVN', 'THERMAX', 'BHEL', 'BEL', 'HAL', 'BEML', 'RVNL', 'ZEEL',
            'SUNTV', 'NETWORK18', 'DELTACORP', 'PVR', 'INOXLEISUR', 'JUBLFOOD', 'TRENT', 'ADITYANB', 'ABFRL', 'RAYMOND'
        ]
        
        result = top_liquid[:count]
        print(f"✅ Selected top {len(result)} liquid stocks")
        return result
    
    def get_sector_wise_stocks(self, sectors: List[str] = None) -> Dict[str, List[str]]:
        """Get stocks by sector"""
        
        if sectors is None:
            sectors = ['IT', 'Banking', 'Auto', 'Pharma', 'FMCG', 'Energy', 'Metals']
        
        print(f"🏭 Getting stocks by sectors: {', '.join(sectors)}")
        
        sector_stocks = {
            'IT': ['TCS', 'INFY', 'WIPRO', 'HCLTECH', 'TECHM', 'LTIM', 'MINDTREE', 'MPHASIS', 'COFORGE', 'PERSISTENT'],
            'Banking': ['HDFCBANK', 'ICICIBANK', 'SBIN', 'KOTAKBANK', 'AXISBANK', 'INDUSINDBK', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB'],
            'Auto': ['MARUTI', 'TATAMOTORS', 'M&M', 'BAJAJ-AUTO', 'EICHERMOT', 'HEROMOTOCO', 'ASHOKLEY', 'TVSMOTOR', 'BALKRISIND', 'MRF'],
            'Pharma': ['SUNPHARMA', 'DRREDDY', 'CIPLA', 'DIVISLAB', 'APOLLOHOSP', 'BIOCON', 'CADILAHC', 'GLENMARK', 'LUPIN', 'TORNTPHARM'],
            'FMCG': ['HINDUNILVR', 'ITC', 'NESTLEIND', 'BRITANNIA', 'TATACONSUM', 'GODREJCP', 'DABUR', 'MARICO', 'COLPAL', 'EMAMILTD'],
            'Energy': ['RELIANCE', 'ONGC', 'BPCL', 'IOC', 'GAIL', 'HINDPETRO', 'MGL', 'IGL', 'PETRONET', 'GSPL'],
            'Metals': ['TATASTEEL', 'JSWSTEEL', 'HINDALCO', 'VEDL', 'SAIL', 'NMDC', 'COALINDIA', 'MOIL', 'JINDALSTEL', 'WELCORP']
        }
        
        result = {}
        for sector in sectors:
            if sector in sector_stocks:
                result[sector] = sector_stocks[sector]
                print(f"   ✅ {sector}: {len(sector_stocks[sector])} stocks")
        
        return result

def main():
    """Test the Nifty stocks fetcher"""
    
    fetcher = NiftyStocksFetcher()
    
    print("🧪 Testing Nifty Stocks Fetcher")
    print("=" * 50)
    
    # Test different index types
    nifty_50 = fetcher.get_intraday_enabled_stocks("nifty_50")
    print(f"Nifty 50: {len(nifty_50)} stocks")
    print(f"Sample: {nifty_50[:10]}")
    
    print()
    
    nifty_100 = fetcher.get_intraday_enabled_stocks("nifty_100")
    print(f"Nifty 100: {len(nifty_100)} stocks")
    print(f"Sample: {nifty_100[:10]}")
    
    print()
    
    top_liquid = fetcher.get_top_liquid_stocks(30)
    print(f"Top 30 Liquid: {len(top_liquid)} stocks")
    print(f"Sample: {top_liquid[:10]}")

if __name__ == "__main__":
    main()
