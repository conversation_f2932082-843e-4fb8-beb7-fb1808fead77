#!/usr/bin/env python3
"""
NSE Intraday Stocks Fetcher
Fetches all stock symbols available for intraday trading from NSE website
"""

import requests
import pandas as pd
from typing import List, Dict, Set
import json
import time
import re
from bs4 import BeautifulSoup
import csv
from io import StringIO

class NSEIntradayStocksFetcher:
    """Fetches intraday-enabled stocks from NSE official website"""
    
    def __init__(self):
        self.base_url = "https://www.nseindia.com"
        self.securities_url = "https://www.nseindia.com/market-data/securities-available-for-trading"
        
        # Headers to mimic browser request
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        # Session for maintaining cookies
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Cache for storing fetched data
        self.cache = {}
        self.cache_timestamp = None
    
    def _get_session_cookies(self):
        """Get session cookies from NSE website"""
        try:
            print("🔐 Getting NSE session cookies...")
            
            # First, visit the main page to get cookies
            response = self.session.get(self.base_url, timeout=15)
            
            if response.status_code == 200:
                print("   ✅ Session established")
                return True
            else:
                print(f"   ❌ Failed to establish session: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error getting session: {e}")
            return False
    
    def fetch_securities_data(self) -> List[Dict]:
        """Fetch securities data from NSE API endpoints"""
        
        print("📊 Fetching securities data from NSE...")
        
        try:
            # Get session first
            if not self._get_session_cookies():
                print("⚠️ Session setup failed, trying direct API calls...")
            
            # Try multiple API endpoints for securities data
            api_endpoints = [
                "/api/equity-stockIndices?index=NIFTY%20500",
                "/api/equity-stockIndices?index=NIFTY%20TOTAL%20MARKET",
                "/api/allIndices",
                "/api/equity-meta-info",
                "/api/equity-stock?index=SECURITIES%20IN%20F%26O"
            ]
            
            all_securities = []
            
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    print(f"   🔍 Trying endpoint: {endpoint}")
                    
                    response = self.session.get(url, timeout=15)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'data' in data and isinstance(data['data'], list):
                            securities = data['data']
                            print(f"   ✅ Found {len(securities)} securities from {endpoint}")
                            
                            for security in securities:
                                if isinstance(security, dict) and 'symbol' in security:
                                    symbol = security['symbol']
                                    
                                    # Skip indices
                                    if not any(idx in symbol for idx in ['NIFTY', 'SENSEX', 'BANKEX']):
                                        all_securities.append({
                                            'symbol': symbol,
                                            'name': security.get('companyName', security.get('meta', {}).get('companyName', '')),
                                            'series': security.get('series', 'EQ'),
                                            'isin': security.get('meta', {}).get('isin', ''),
                                            'industry': security.get('meta', {}).get('industry', ''),
                                            'source': endpoint
                                        })
                            
                            if len(securities) > 100:  # Good data source found
                                break
                    else:
                        print(f"   ❌ Endpoint failed: {response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ Error with endpoint {endpoint}: {e}")
                    continue
            
            if all_securities:
                print(f"✅ Total securities collected: {len(all_securities)}")
                return all_securities
            else:
                print("⚠️ No securities found from API, trying fallback methods...")
                return self._fetch_fallback_securities()
                
        except Exception as e:
            print(f"❌ Error fetching securities data: {e}")
            return self._fetch_fallback_securities()
    
    def _fetch_fallback_securities(self) -> List[Dict]:
        """Fallback method to get securities data"""
        
        print("🔄 Using fallback securities data...")
        
        # Comprehensive list of NSE stocks known to be available for intraday
        fallback_securities = [
            # Nifty 50
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK',
            'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH',
            'BAJFINANCE', 'POWERGRID', 'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA',
            'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJFINSV', 'BAJAJ-AUTO',
            'BRITANNIA', 'COALINDIA', 'DIVISLAB', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS',
            
            # Nifty Next 50
            'GODREJCP', 'PIDILITIND', 'DABUR', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'AMBUJACEM', 'ACC', 'SHREECEM', 'RAMCOCEM',
            'VEDL', 'SAIL', 'NMDC', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK', 'BANDHANBNK',
            'AUBANK', 'CHOLAFIN', 'MUTHOOTFIN', 'PFC', 'RECLTD', 'IRCTC', 'CONCOR', 'GMRINFRA', 'ADANIGREEN', 'TATAPOWER',
            'TORNTPOWER', 'NHPC', 'SJVN', 'THERMAX', 'BHEL', 'BEL', 'HAL', 'BEML', 'RVNL', 'ZEEL',
            'SUNTV', 'NETWORK18', 'DELTACORP', 'PVR', 'INOXLEISUR', 'JUBLFOOD', 'TRENT', 'ADITYANB', 'ABFRL', 'RAYMOND',
            
            # Additional liquid stocks
            'VMART', 'SHOPERSTOP', 'RELAXO', 'BATA', 'CROMPTON', 'HAVELLS', 'VOLTAS', 'BLUESTARCO', 'WHIRLPOOL', 'DIXON',
            'LALPATHLAB', 'METROPOLIS', 'THYROCARE', 'KRBL', 'LTF', 'DCMSHRIRAM', 'CHAMBLFERT', 'COROMANDEL', 'GNFC', 'NFL',
            'RCF', 'FACT', 'GSFC', 'MADRASFERT', 'DEEPAKNTR', 'AARTI', 'ALKYLAMINE', 'BALRAMCHIN', 'CHEMCON', 'CLEAN',
            'FINEORG', 'GALAXYSURF', 'GHCL', 'GUJALKALI', 'GULFOILLUB', 'HATSUN', 'HEIDELBERG', 'HONAUT', 'IPCALAB', 'JKCEMENT',
            'JKLAKSHMI', 'JYOTHYLAB', 'KAJARIACER', 'KANSAINER', 'KPRMILL', 'LAOPALA', 'MAHSEAMLES', 'MANAPPURAM', 'MINDACORP',
            'MOLDTKPAC', 'MPHASIS', 'NATCOPHARM', 'NAVINFLUOR', 'NESCO', 'NILKAMAL', 'NOCIL', 'ORIENTCEM', 'PERSISTENT', 'PFIZER',
            'PHOENIXLTD', 'PIRAMALENT', 'POLYCAB', 'POLYMED', 'PRAJIND', 'PRSMJOHNSN', 'RADICO', 'RAIN', 'RAJESHEXPO', 'RATNAMANI',
            'REDINGTON', 'RELAXO', 'RESPONIND', 'RITES', 'ROSSARI', 'ROUTE', 'RUPA', 'SCHAEFFLER', 'SEQUENT', 'SFL',
            'SHANKARA', 'SHILPAMED', 'SHOPERSTOP', 'SIEMENS', 'SOBHA', 'SONACOMS', 'STARCEMENT', 'SUDARSCHEM', 'SUNDARMFIN', 'SUNDRMFAST',
            'SUPRAJIT', 'SUPRIYA', 'SYMPHONY', 'TAKE', 'TATACHEM', 'TATACOMM', 'TATAELXSI', 'TATAINVEST', 'TATATECH', 'TEAMLEASE',
            'TECHM', 'TEXRAIL', 'THYROCARE', 'TIINDIA', 'TIMKEN', 'TIPSINDLTD', 'TITAGARH', 'TNPETRO', 'TRENT', 'TRIDENT',
            'TRITURBINE', 'TTKPRESTIG', 'TVSMOTOR', 'UCOBANK', 'UJJIVAN', 'UJJIVANSFB', 'ULTRACEMCO', 'UNIONBANK', 'UPL', 'UTIAMC',
            'VAIBHAVGBL', 'VARROC', 'VBL', 'VEDL', 'VENKEYS', 'VGUARD', 'VINATIORGA', 'VIPIND', 'VMART', 'VOLTAS',
            'VTL', 'WABCOINDIA', 'WELCORP', 'WELSPUNIND', 'WESTLIFE', 'WHIRLPOOL', 'WIPRO', 'WOCKPHARMA', 'YESBANK', 'ZEEL',
            'ZENSARTECH', 'ZFCVINDIA', 'ZODIACLOTH', 'ZOMATO', 'ZYDUSLIFE'
        ]
        
        securities_data = []
        for symbol in fallback_securities:
            securities_data.append({
                'symbol': symbol,
                'name': '',
                'series': 'EQ',
                'isin': '',
                'industry': '',
                'source': 'fallback'
            })
        
        print(f"✅ Fallback securities loaded: {len(securities_data)}")
        return securities_data
    
    def get_intraday_enabled_stocks(self, limit: int = None) -> List[str]:
        """Get all stocks enabled for intraday trading"""
        
        print("🎯 Fetching intraday-enabled stocks from NSE...")
        
        try:
            # Check cache first
            if self.cache and self.cache_timestamp:
                time_diff = time.time() - self.cache_timestamp
                if time_diff < 3600:  # Cache for 1 hour
                    print("📋 Using cached data...")
                    securities = self.cache.get('securities', [])
                else:
                    securities = self.fetch_securities_data()
                    self.cache = {'securities': securities}
                    self.cache_timestamp = time.time()
            else:
                securities = self.fetch_securities_data()
                self.cache = {'securities': securities}
                self.cache_timestamp = time.time()
            
            # Extract symbols and filter for intraday trading
            intraday_stocks = []
            
            for security in securities:
                symbol = security.get('symbol', '')
                series = security.get('series', 'EQ')
                
                # Filter criteria for intraday trading
                if (symbol and 
                    series in ['EQ', 'BE'] and  # Equity series
                    len(symbol) <= 20 and  # Reasonable symbol length
                    not any(exclude in symbol.upper() for exclude in [
                        'NIFTY', 'SENSEX', 'BANKEX', 'INDEX', 'ETF', 'GOLD', 'SILVER'
                    ])):
                    
                    intraday_stocks.append(symbol)
            
            # Remove duplicates while preserving order
            seen = set()
            unique_stocks = []
            for stock in intraday_stocks:
                if stock not in seen:
                    seen.add(stock)
                    unique_stocks.append(stock)
            
            # Apply limit if specified
            if limit:
                unique_stocks = unique_stocks[:limit]
            
            print(f"✅ Found {len(unique_stocks)} intraday-enabled stocks")
            
            if unique_stocks:
                print(f"📋 Sample stocks: {', '.join(unique_stocks[:10])}")
                if len(unique_stocks) > 10:
                    print(f"   ... and {len(unique_stocks) - 10} more")
            
            return unique_stocks
            
        except Exception as e:
            print(f"❌ Error getting intraday stocks: {e}")
            print("🔄 Using minimal fallback list...")
            
            # Minimal fallback
            return ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']
    
    def get_stocks_by_criteria(self, 
                              min_market_cap: str = None,
                              sectors: List[str] = None,
                              series: List[str] = None,
                              limit: int = 100) -> List[str]:
        """Get stocks based on specific criteria"""
        
        print(f"🔍 Filtering stocks by criteria...")
        
        if sectors:
            print(f"   🏭 Sectors: {', '.join(sectors)}")
        if series:
            print(f"   📊 Series: {', '.join(series)}")
        if min_market_cap:
            print(f"   💰 Min Market Cap: {min_market_cap}")
        
        # Get all stocks first
        all_stocks = self.get_intraday_enabled_stocks()
        
        # For now, return all stocks as we don't have detailed filtering
        # In a real implementation, you would filter by market cap, sector, etc.
        
        filtered_stocks = all_stocks[:limit] if limit else all_stocks
        
        print(f"✅ Filtered to {len(filtered_stocks)} stocks")
        return filtered_stocks
    
    def save_stocks_to_file(self, stocks: List[str], filename: str = "nse_intraday_stocks.txt"):
        """Save stocks list to file"""
        
        try:
            with open(filename, 'w') as f:
                f.write("# NSE Intraday-Enabled Stocks\n")
                f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total Stocks: {len(stocks)}\n\n")
                
                for i, stock in enumerate(stocks, 1):
                    f.write(f"{stock}\n")
            
            print(f"💾 Stocks saved to: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving stocks: {e}")
            return False
    
    def get_top_volume_stocks(self, limit: int = 50) -> List[str]:
        """Get stocks with highest trading volume (proxy for liquidity)"""
        
        print(f"💧 Getting top {limit} high-volume stocks...")
        
        # These are typically the most liquid stocks for intraday trading
        high_volume_stocks = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'SBIN', 'BHARTIARTL', 'ITC', 'KOTAKBANK', 'HINDUNILVR',
            'LT', 'AXISBANK', 'MARUTI', 'ASIANPAINT', 'SUNPHARMA', 'BAJFINANCE', 'TITAN', 'ULTRACEMCO', 'WIPRO', 'HCLTECH',
            'POWERGRID', 'NTPC', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'TECHM', 'NESTLEIND', 'ADANIENT', 'BAJAJFINSV', 'CIPLA',
            'COALINDIA', 'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJ-AUTO',
            'BRITANNIA', 'DIVISLAB', 'APOLLOHOSP', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS'
        ]
        
        result = high_volume_stocks[:limit]
        print(f"✅ Selected {len(result)} high-volume stocks")
        return result

def main():
    """Test the NSE intraday stocks fetcher"""
    
    print("🇮🇳 NSE INTRADAY STOCKS FETCHER")
    print("=" * 50)
    
    fetcher = NSEIntradayStocksFetcher()
    
    # Test fetching all intraday stocks
    print("\n📊 Fetching all intraday-enabled stocks...")
    all_stocks = fetcher.get_intraday_enabled_stocks(limit=100)
    
    print(f"\n✅ Results:")
    print(f"   Total stocks: {len(all_stocks)}")
    print(f"   First 20: {', '.join(all_stocks[:20])}")
    
    # Save to file
    fetcher.save_stocks_to_file(all_stocks, "nse_intraday_stocks_test.txt")
    
    # Test high-volume stocks
    print(f"\n💧 Testing high-volume stocks...")
    volume_stocks = fetcher.get_top_volume_stocks(30)
    print(f"   High-volume stocks: {', '.join(volume_stocks[:10])}")
    
    print(f"\n🎯 Ready for integration with agentic analyzer!")

if __name__ == "__main__":
    main()
