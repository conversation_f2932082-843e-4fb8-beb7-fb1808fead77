"""
Indian News Analysis Agent for CrewAI
Specialized agent for analyzing Indian financial news and sentiment
"""

from crewai_tools import BaseTool
from typing import Dict, Any, List
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.indian_news_aggregator import IndianNewsAggregator
from tools.nlp_processor import NLPProcessor

class IndianNewsAgent(BaseTool):
    name: str = "Indian Financial News Analyst"
    description: str = """
    Analyzes comprehensive Indian financial news and sentiment from major sources:
    - MoneyControl (Indian market news and sector updates)
    - Bloomberg Quint (global market impact on India)
    - Economic Times (financial news analysis)
    - Business Standard (market insights)
    - Investing.com India (economic calendar and trends)
    
    Provides sentiment analysis, stock-specific news impact, and market mood assessment
    specifically for Indian stock market trading decisions.
    """
    
    def __init__(self):
        super().__init__()
        self.news_aggregator = IndianNewsAggregator()
        self.nlp_processor = NLPProcessor()
    
    def _run(self, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Analyze Indian financial news and sentiment
        
        Args:
            analysis_type: Type of analysis (comprehensive, quick, sector-specific)
            
        Returns:
            Dictionary containing news analysis and sentiment
        """
        
        try:
            print(f"📰 Analyzing Indian financial news...")
            
            # Get comprehensive Indian news
            general_news = self.news_aggregator.get_comprehensive_indian_news(10)
            
            # Analyze overall sentiment
            all_news_text = ' '.join([
                item['title'] + ' ' + item.get('summary', '') 
                for item in general_news
            ])
            
            news_sentiment = self.nlp_processor.analyze_sentiment(all_news_text)
            
            # Get sector-specific news
            sector_news = {}
            key_sectors = ['banking', 'it', 'pharma', 'auto', 'fmcg']
            
            for sector in key_sectors:
                sector_specific = self.news_aggregator.get_sector_specific_news(sector, 3)
                if sector_specific:
                    sector_news[sector] = sector_specific
            
            # Analyze stock mentions
            stock_mentions = {}
            indian_stocks = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 
                           'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK']
            
            for stock in indian_stocks:
                stock_news = self.news_aggregator.get_stock_specific_news(stock, 3)
                if stock_news:
                    stock_mentions[stock] = {
                        'news_count': len(stock_news),
                        'news_items': stock_news,
                        'sentiment': self._analyze_stock_sentiment(stock_news)
                    }
            
            # Compile news analysis
            news_analysis = {
                'overall_sentiment': {
                    'sentiment': news_sentiment.get('sentiment', 'neutral'),
                    'score': news_sentiment.get('score', 0),
                    'confidence': news_sentiment.get('confidence', 'low')
                },
                'general_news': {
                    'count': len(general_news),
                    'items': general_news[:5],  # Top 5 for summary
                    'sources': list(set([item['source'] for item in general_news]))
                },
                'sector_news': sector_news,
                'stock_mentions': stock_mentions,
                'market_mood': self._assess_market_mood(news_sentiment, general_news),
                'key_themes': self._extract_key_themes(general_news),
                'timestamp': general_news[0]['timestamp'] if general_news else '',
                'analysis_type': analysis_type
            }
            
            # Display summary
            sentiment_emoji = "🟢" if news_sentiment.get('sentiment') == 'positive' else "🔴" if news_sentiment.get('sentiment') == 'negative' else "🟡"
            print(f"   📊 Overall Sentiment: {news_sentiment.get('sentiment', 'neutral').upper()} {sentiment_emoji}")
            print(f"   📰 News Sources: {len(news_analysis['general_news']['sources'])}")
            print(f"   📈 Stocks in News: {len(stock_mentions)}")
            print(f"   🏭 Sectors Covered: {len(sector_news)}")
            
            return news_analysis
            
        except Exception as e:
            print(f"❌ Error in Indian news analysis: {e}")
            return {
                'error': str(e),
                'overall_sentiment': {'sentiment': 'neutral', 'score': 0, 'confidence': 'low'},
                'general_news': {'count': 0, 'items': [], 'sources': []},
                'sector_news': {},
                'stock_mentions': {},
                'market_mood': 'uncertain',
                'key_themes': [],
                'timestamp': '',
                'analysis_type': analysis_type
            }
    
    def _analyze_stock_sentiment(self, stock_news: List[Dict]) -> Dict[str, Any]:
        """Analyze sentiment for specific stock news"""
        if not stock_news:
            return {'sentiment': 'neutral', 'score': 0}
        
        combined_text = ' '.join([item['title'] for item in stock_news])
        return self.nlp_processor.analyze_sentiment(combined_text)
    
    def _assess_market_mood(self, sentiment: Dict, news_items: List[Dict]) -> str:
        """Assess overall market mood"""
        sentiment_score = sentiment.get('score', 0)
        news_count = len(news_items)
        
        if sentiment_score > 0.3 and news_count > 5:
            return 'bullish'
        elif sentiment_score < -0.3 and news_count > 5:
            return 'bearish'
        elif abs(sentiment_score) < 0.1:
            return 'neutral'
        else:
            return 'mixed'
    
    def _extract_key_themes(self, news_items: List[Dict]) -> List[str]:
        """Extract key themes from news"""
        themes = []
        
        # Common Indian market themes
        theme_keywords = {
            'rbi_policy': ['rbi', 'policy', 'interest rate', 'monetary'],
            'earnings': ['earnings', 'results', 'profit', 'revenue'],
            'government': ['government', 'budget', 'policy', 'regulation'],
            'global_impact': ['global', 'us', 'china', 'fed', 'international'],
            'sector_rotation': ['sector', 'banking', 'it', 'pharma', 'auto'],
            'fii_activity': ['fii', 'foreign', 'investment', 'inflow', 'outflow']
        }
        
        all_text = ' '.join([item['title'].lower() for item in news_items])
        
        for theme, keywords in theme_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                themes.append(theme.replace('_', ' ').title())
        
        return themes[:5]  # Return top 5 themes
    
    def get_sector_sentiment(self, sector: str) -> Dict[str, Any]:
        """Get sentiment for specific sector"""
        try:
            sector_news = self.news_aggregator.get_sector_specific_news(sector, 5)
            if sector_news:
                combined_text = ' '.join([item['title'] for item in sector_news])
                return self.nlp_processor.analyze_sentiment(combined_text)
            return {'sentiment': 'neutral', 'score': 0}
        except Exception as e:
            print(f"❌ Error analyzing {sector} sentiment: {e}")
            return {'sentiment': 'neutral', 'score': 0}
    
    def get_stock_news_impact(self, symbol: str) -> Dict[str, Any]:
        """Get news impact for specific stock"""
        try:
            stock_news = self.news_aggregator.get_stock_specific_news(symbol, 5)
            if stock_news:
                sentiment = self._analyze_stock_sentiment(stock_news)
                return {
                    'symbol': symbol,
                    'news_count': len(stock_news),
                    'sentiment': sentiment,
                    'recent_news': stock_news[:3]
                }
            return {'symbol': symbol, 'news_count': 0, 'sentiment': {'sentiment': 'neutral', 'score': 0}}
        except Exception as e:
            print(f"❌ Error analyzing {symbol} news: {e}")
            return {'symbol': symbol, 'news_count': 0, 'sentiment': {'sentiment': 'neutral', 'score': 0}}
