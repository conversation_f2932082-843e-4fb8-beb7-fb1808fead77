#!/usr/bin/env python3
"""
Equity CSV Stocks Fetcher
Fetches all stock symbols from EQUITY_L.csv file for intraday trading
"""

import pandas as pd
import csv
from typing import List, Dict, Set
import os
from pathlib import Path

class EquityCSVStocksFetcher:
    """Fetches intraday-enabled stocks from EQUITY_L.csv file"""
    
    def __init__(self, csv_file_path: str = None):
        # Default path to EQUITY_L.csv
        if csv_file_path is None:
            # Look for the file in the project root
            project_root = Path(__file__).parent.parent
            csv_file_path = project_root / "EQUITY_L.csv"
        
        self.csv_file_path = str(csv_file_path)
        
        # Cache for storing loaded data
        self.cache = {}
        self.cache_loaded = False
        
        print(f"📁 CSV file path: {self.csv_file_path}")
    
    def _load_equity_data(self) -> List[Dict]:
        """Load equity data from CSV file"""
        
        if self.cache_loaded and 'equity_data' in self.cache:
            print("📋 Using cached equity data...")
            return self.cache['equity_data']
        
        try:
            print("📊 Loading equity data from CSV file...")
            
            if not os.path.exists(self.csv_file_path):
                raise FileNotFoundError(f"EQUITY_L.csv not found at: {self.csv_file_path}")
            
            # Read CSV file
            equity_data = []
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                
                for row in csv_reader:
                    # Clean and extract data
                    symbol = row.get('SYMBOL', '').strip()
                    company_name = row.get('NAME OF COMPANY', '').strip()
                    series = row.get(' SERIES', '').strip()  # Note the space in column name
                    listing_date = row.get(' DATE OF LISTING', '').strip()
                    isin = row.get(' ISIN NUMBER', '').strip()
                    face_value = row.get(' FACE VALUE', '').strip()
                    
                    if symbol:  # Only add if symbol exists
                        equity_data.append({
                            'symbol': symbol,
                            'company_name': company_name,
                            'series': series,
                            'listing_date': listing_date,
                            'isin': isin,
                            'face_value': face_value
                        })
            
            print(f"✅ Loaded {len(equity_data)} equity records from CSV")
            
            # Cache the data
            self.cache['equity_data'] = equity_data
            self.cache_loaded = True
            
            return equity_data
            
        except Exception as e:
            print(f"❌ Error loading equity data: {e}")
            return []
    
    def get_all_equity_symbols(self) -> List[str]:
        """Get all equity symbols from the CSV file"""
        
        print("📊 Fetching all equity symbols...")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            print("❌ No equity data available")
            return []
        
        symbols = [record['symbol'] for record in equity_data if record['symbol']]
        
        print(f"✅ Found {len(symbols)} total equity symbols")
        return symbols
    
    def get_intraday_enabled_stocks(self, limit: int = None) -> List[str]:
        """Get stocks enabled for intraday trading"""
        
        print("🎯 Fetching intraday-enabled stocks...")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            print("❌ No equity data available")
            return []
        
        # Filter for intraday-suitable stocks
        intraday_stocks = []
        
        for record in equity_data:
            symbol = record['symbol']
            series = record['series']
            
            # Filter criteria for intraday trading
            if (symbol and 
                series in ['EQ', 'BE'] and  # Equity and BE series
                len(symbol) <= 20 and  # Reasonable symbol length
                not any(exclude in symbol.upper() for exclude in [
                    'NIFTY', 'SENSEX', 'BANKEX', 'INDEX', 'ETF', 'GOLD', 'SILVER', 'FUND'
                ])):
                
                intraday_stocks.append(symbol)
        
        # Apply limit if specified
        if limit:
            intraday_stocks = intraday_stocks[:limit]
        
        print(f"✅ Found {len(intraday_stocks)} intraday-enabled stocks")
        
        if intraday_stocks:
            print(f"📋 Sample stocks: {', '.join(intraday_stocks[:10])}")
            if len(intraday_stocks) > 10:
                print(f"   ... and {len(intraday_stocks) - 10} more")
        
        return intraday_stocks
    
    def get_stocks_by_series(self, series_list: List[str] = None, limit: int = None) -> List[str]:
        """Get stocks filtered by series (EQ, BE, etc.)"""
        
        if series_list is None:
            series_list = ['EQ']  # Default to EQ series
        
        print(f"📊 Fetching stocks from series: {', '.join(series_list)}")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            return []
        
        filtered_stocks = []
        
        for record in equity_data:
            symbol = record['symbol']
            series = record['series']
            
            if symbol and series in series_list:
                filtered_stocks.append(symbol)
        
        # Apply limit if specified
        if limit:
            filtered_stocks = filtered_stocks[:limit]
        
        print(f"✅ Found {len(filtered_stocks)} stocks in specified series")
        return filtered_stocks
    
    def get_stocks_by_name_pattern(self, pattern: str, limit: int = None) -> List[str]:
        """Get stocks whose company names match a pattern"""
        
        print(f"🔍 Searching stocks with name pattern: '{pattern}'")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            return []
        
        matching_stocks = []
        
        for record in equity_data:
            symbol = record['symbol']
            company_name = record['company_name'].upper()
            
            if symbol and pattern.upper() in company_name:
                matching_stocks.append(symbol)
        
        # Apply limit if specified
        if limit:
            matching_stocks = matching_stocks[:limit]
        
        print(f"✅ Found {len(matching_stocks)} stocks matching pattern")
        return matching_stocks
    
    def get_liquid_stocks(self, limit: int = 100) -> List[str]:
        """Get most liquid stocks (based on known liquid symbols)"""
        
        print(f"💧 Getting top {limit} liquid stocks...")
        
        # Get all available stocks
        all_stocks = self.get_intraday_enabled_stocks()
        
        # Known liquid stocks (in order of liquidity)
        liquid_symbols = [
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN', 'BHARTIARTL', 'KOTAKBANK',
            'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA', 'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH',
            'BAJFINANCE', 'POWERGRID', 'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA',
            'DRREDDY', 'EICHERMOT', 'GRASIM', 'HEROMOTOCO', 'HINDALCO', 'INDUSINDBK', 'JSWSTEEL', 'M&M', 'BAJAJFINSV', 'BAJAJ-AUTO',
            'BRITANNIA', 'COALINDIA', 'DIVISLAB', 'HDFCLIFE', 'SBILIFE', 'SHRIRAMFIN', 'TATACONSUM', 'UPL', 'LTIM', 'ADANIPORTS',
            'GODREJCP', 'PIDILITIND', 'DABUR', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'AMBUJACEM', 'ACC', 'SHREECEM', 'RAMCOCEM',
            'VEDL', 'SAIL', 'NMDC', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK', 'BANDHANBNK',
            'AUBANK', 'CHOLAFIN', 'MUTHOOTFIN', 'PFC', 'RECLTD', 'IRCTC', 'CONCOR', 'GMRINFRA', 'ADANIGREEN', 'TATAPOWER',
            'TORNTPOWER', 'NHPC', 'SJVN', 'THERMAX', 'BHEL', 'BEL', 'HAL', 'BEML', 'RVNL', 'ZEEL'
        ]
        
        # Filter liquid symbols that exist in our CSV data
        available_liquid = []
        all_stocks_set = set(all_stocks)
        
        for symbol in liquid_symbols:
            if symbol in all_stocks_set:
                available_liquid.append(symbol)
        
        # If we need more stocks, add from the remaining available stocks
        if len(available_liquid) < limit:
            remaining_needed = limit - len(available_liquid)
            remaining_stocks = [s for s in all_stocks if s not in available_liquid]
            available_liquid.extend(remaining_stocks[:remaining_needed])
        
        # Apply final limit
        result = available_liquid[:limit]
        
        print(f"✅ Selected {len(result)} liquid stocks")
        return result
    
    def get_sector_stocks(self, sector_keywords: List[str], limit: int = None) -> List[str]:
        """Get stocks from specific sectors based on company name keywords"""
        
        print(f"🏭 Searching stocks in sectors: {', '.join(sector_keywords)}")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            return []
        
        sector_stocks = []
        
        for record in equity_data:
            symbol = record['symbol']
            company_name = record['company_name'].upper()
            
            if symbol:
                # Check if any sector keyword is in company name
                for keyword in sector_keywords:
                    if keyword.upper() in company_name:
                        sector_stocks.append(symbol)
                        break
        
        # Apply limit if specified
        if limit:
            sector_stocks = sector_stocks[:limit]
        
        print(f"✅ Found {len(sector_stocks)} stocks in specified sectors")
        return sector_stocks
    
    def get_statistics(self) -> Dict:
        """Get statistics about the equity data"""
        
        print("📊 Generating equity data statistics...")
        
        equity_data = self._load_equity_data()
        
        if not equity_data:
            return {}
        
        # Count by series
        series_count = {}
        for record in equity_data:
            series = record['series']
            series_count[series] = series_count.get(series, 0) + 1
        
        # Total symbols
        total_symbols = len(equity_data)
        eq_symbols = series_count.get('EQ', 0)
        be_symbols = series_count.get('BE', 0)
        
        stats = {
            'total_symbols': total_symbols,
            'eq_series': eq_symbols,
            'be_series': be_symbols,
            'other_series': total_symbols - eq_symbols - be_symbols,
            'series_breakdown': series_count,
            'intraday_suitable': eq_symbols + be_symbols
        }
        
        print(f"📊 Statistics:")
        print(f"   Total symbols: {stats['total_symbols']}")
        print(f"   EQ series: {stats['eq_series']}")
        print(f"   BE series: {stats['be_series']}")
        print(f"   Intraday suitable: {stats['intraday_suitable']}")
        
        return stats
    
    def save_stocks_to_file(self, stocks: List[str], filename: str = "equity_stocks_list.txt"):
        """Save stocks list to file in reports folder"""

        try:
            # Create reports directory if it doesn't exist
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # Save to reports folder
            filepath = os.path.join(reports_dir, filename)

            with open(filepath, 'w') as f:
                f.write("# Equity Stocks from EQUITY_L.csv\n")
                f.write(f"# Total Stocks: {len(stocks)}\n\n")

                for i, stock in enumerate(stocks, 1):
                    f.write(f"{stock}\n")

            print(f"💾 Stocks saved to: {filepath}")
            return True

        except Exception as e:
            print(f"❌ Error saving stocks: {e}")
            return False

def main():
    """Test the Equity CSV stocks fetcher"""
    
    print("📊 EQUITY CSV STOCKS FETCHER")
    print("=" * 50)
    
    fetcher = EquityCSVStocksFetcher()
    
    # Get statistics
    stats = fetcher.get_statistics()
    
    # Test different fetching methods
    print(f"\n🎯 Testing intraday-enabled stocks (limit 50)...")
    intraday_stocks = fetcher.get_intraday_enabled_stocks(limit=50)
    print(f"   Sample: {', '.join(intraday_stocks[:10])}")
    
    print(f"\n💧 Testing liquid stocks (limit 30)...")
    liquid_stocks = fetcher.get_liquid_stocks(limit=30)
    print(f"   Sample: {', '.join(liquid_stocks[:10])}")
    
    print(f"\n📊 Testing EQ series stocks (limit 100)...")
    eq_stocks = fetcher.get_stocks_by_series(['EQ'], limit=100)
    print(f"   Count: {len(eq_stocks)}")
    print(f"   Sample: {', '.join(eq_stocks[:10])}")
    
    # Save to file
    fetcher.save_stocks_to_file(intraday_stocks, "equity_intraday_stocks_test.txt")
    
    print(f"\n✅ Testing complete!")
    print(f"🎯 Ready for integration with agentic analyzer!")

if __name__ == "__main__":
    main()
