"""
Report Generation Agent for CrewAI
Specialized agent for generating reports matching original system format
"""

from crewai_tools import BaseTool
from typing import Dict, Any
import json
from datetime import datetime

class ReportGenerationAgent(BaseTool):
    name: str = "Indian Market Report Specialist"
    description: str = """
    Generates comprehensive reports matching the exact format of the original system.
    Ensures output format exactly matches with proper emojis, formatting, and 
    Indian market specific recommendations maintaining consistency.
    """
    
    def _run(self, all_data: str) -> str:
        """Generate final report matching original system format"""
        
        try:
            print(f"📋 Generating comprehensive Indian market report...")
            
            # Parse all input data
            if isinstance(all_data, str):
                try:
                    data = json.loads(all_data)
                except:
                    data = {}
            else:
                data = all_data
            
            # Extract components
            market_data = data.get('market_data', {})
            news_data = data.get('news_data', {})
            technical_data = data.get('technical_data', {})
            strategy_data = data.get('strategy_data', {})
            timing_data = data.get('timing_data', {})
            risk_data = data.get('risk_data', {})
            
            # Determine report type
            market_status = timing_data.get('market_status', 'UNKNOWN')
            is_live = timing_data.get('is_live_market', False)
            
            if is_live:
                report = self._generate_live_report(
                    market_data, news_data, technical_data, strategy_data, timing_data, risk_data
                )
            else:
                report = self._generate_predictive_report(
                    market_data, news_data, technical_data, strategy_data, timing_data, risk_data
                )
            
            print(f"✅ Report generated successfully")
            
            return report
            
        except Exception as e:
            print(f"❌ Error generating report: {e}")
            return f"❌ Error generating report: {str(e)}"
    
    def _generate_live_report(self, market_data, news_data, technical_data, strategy_data, timing_data, risk_data) -> str:
        """Generate live market report"""
        
        current_time = timing_data.get('current_time', datetime.now().strftime('%H:%M:%S IST'))
        
        report = f"""🔴 LIVE TRADING SIGNALS - {current_time}

🚨 URGENT ALERTS:"""
        
        # Generate urgent alerts
        urgent_alerts = self._get_urgent_alerts(strategy_data, timing_data)
        if urgent_alerts:
            for alert in urgent_alerts:
                report += f"\n   ⚡ {alert}"
        else:
            report += "\n   📊 No urgent alerts at this time"
        
        # Market snapshot
        report += f"\n\n📊 MARKET SNAPSHOT:"
        indices = market_data.get('indices', {})
        
        for index_name, data in list(indices.items())[:3]:  # Top 3 indices
            change = data.get('change', 0)
            change_percent = data.get('change_percent', 0)
            value = data.get('value', 0)
            emoji = "🟢" if change >= 0 else "🔴"
            report += f"\n   {index_name}: {value:.2f} ({change:+.2f}, {change_percent:+.2f}%) {emoji}"
        
        # Live trading signals
        report += f"\n\n🎯 LIVE TRADING SIGNALS:"
        
        recommendations = strategy_data.get('overall_recommendations', [])
        
        if recommendations:
            for i, rec in enumerate(recommendations[:5], 1):  # Top 5
                symbol = rec['symbol']
                action = rec['action']
                confidence = rec['confidence']
                score = rec.get('score', 0)
                entry_price = rec.get('entry_price', 0)
                target = rec.get('target', 0)
                stop_loss = rec.get('stop_loss', 0)
                reason = rec.get('reason', '')
                
                # Get urgency from timing
                urgency = self._get_signal_urgency(score, confidence, timing_data)
                urgency_emoji = "🚨" if urgency == 'HIGH' else "⚡" if urgency == 'MEDIUM' else "📊"
                
                report += f"\n\n{i}. {symbol} - {action} {urgency_emoji}"
                report += f"\n   💰 Entry: ₹{entry_price:.2f}"
                report += f"\n   🎯 Confidence: {confidence}"
                
                if target:
                    report += f"\n   🎯 Target: ₹{target:.2f}"
                if stop_loss:
                    report += f"\n   🛡️ Stop: ₹{stop_loss:.2f}"
                
                report += f"\n   💡 Reason: {reason}"
        else:
            report += "\n\n   📊 No high-confidence signals at this time"
            report += "\n   ⏰ Wait for next optimal execution window"
        
        return report
    
    def _generate_predictive_report(self, market_data, news_data, technical_data, strategy_data, timing_data, risk_data) -> str:
        """Generate predictive analysis report"""
        
        current_time = timing_data.get('current_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S IST'))
        
        report = f"""🔮 PREDICTIVE ANALYSIS - {current_time}

📰 NEWS SENTIMENT:"""
        
        # News sentiment
        overall_sentiment = news_data.get('overall_sentiment', {})
        sentiment = overall_sentiment.get('sentiment', 'neutral')
        sentiment_emoji = "🟢" if sentiment == 'positive' else "🔴" if sentiment == 'negative' else "🟡"
        
        report += f" {sentiment.upper()} {sentiment_emoji}"
        
        # Key headlines
        general_news = news_data.get('general_news', {})
        news_items = general_news.get('items', [])
        
        if news_items:
            report += "\n   Key Headlines:"
            for item in news_items[:3]:
                title = item.get('title', '')[:80] + "..." if len(item.get('title', '')) > 80 else item.get('title', '')
                report += f"\n   • {title}"
        
        # Stocks in news
        stock_mentions = news_data.get('stock_mentions', {})
        if stock_mentions:
            mentioned_stocks = list(stock_mentions.keys())[:5]
            report += f"\n   📈 Stocks in news: {', '.join(mentioned_stocks)}"
        
        # Tomorrow's watchlist
        report += f"\n\n⭐ TOMORROW'S WATCHLIST:"
        
        best_opportunities = strategy_data.get('best_opportunities', [])
        
        if best_opportunities:
            for opp in best_opportunities[:5]:
                symbol = opp['symbol']
                signal = opp['signal']
                entry_price = opp.get('entry_price', 0)
                reason = opp.get('reason', '')
                
                report += f"\n   🎯 {symbol} - {signal} at ₹{entry_price:.2f}"
                report += f"\n      Reason: {reason}"
        else:
            report += "\n   📊 No high-confidence opportunities identified"
            report += "\n   🔍 Continue monitoring for better setups"
        
        # Stock predictions
        report += f"\n\n🔮 STOCK PREDICTIONS:"
        
        strategy_results = strategy_data.get('strategy_results', {})
        
        if strategy_results:
            for i, (symbol, strategies) in enumerate(list(strategy_results.items())[:5], 1):
                best_strategy = self._get_best_strategy(strategies)
                
                if best_strategy:
                    prediction = self._convert_signal_to_prediction(best_strategy['signal'])
                    confidence = best_strategy['confidence']
                    entry_price = best_strategy.get('entry_price', 0)
                    target = best_strategy.get('target', 0)
                    stop_loss = best_strategy.get('stop_loss', 0)
                    
                    prediction_emoji = "🟢" if prediction == 'UP' else "🔴" if prediction == 'DOWN' else "🟡"
                    
                    report += f"\n\n{i}. {symbol} - {prediction} {prediction_emoji}"
                    report += f"\n   💰 Last Price: ₹{entry_price:.2f}"
                    report += f"\n   🎯 Confidence: {confidence}"
                    
                    if target and stop_loss:
                        expected_high = max(target, entry_price)
                        expected_low = min(stop_loss, entry_price)
                        report += f"\n   📊 Expected Range: ₹{expected_low:.2f} - ₹{expected_high:.2f}"
                    
                    # Watch for levels
                    if prediction == 'UP' and target:
                        report += f"\n   🔍 Watch for: Break above ₹{entry_price * 1.01:.2f}"
                    elif prediction == 'DOWN' and stop_loss:
                        report += f"\n   🔍 Watch for: Break below ₹{entry_price * 0.99:.2f}"
                    else:
                        report += f"\n   🔍 Watch for: Range trading"
        
        return report
    
    def _get_urgent_alerts(self, strategy_data, timing_data) -> list:
        """Get urgent alerts for live market"""
        
        alerts = []
        
        # Check for high urgency timing
        urgency = timing_data.get('urgency_level', 'LOW')
        
        if urgency == 'HIGH' or urgency == 'CRITICAL':
            execution_window = timing_data.get('execution_window', '')
            alerts.append(f"OPTIMAL EXECUTION TIME - {execution_window}")
        
        # Check for high-confidence signals
        recommendations = strategy_data.get('overall_recommendations', [])
        
        for rec in recommendations:
            if rec.get('confidence') == 'High' and rec.get('score', 0) >= 8:
                symbol = rec['symbol']
                action = rec['action']
                reason = rec.get('reason', '')[:50] + "..." if len(rec.get('reason', '')) > 50 else rec.get('reason', '')
                alerts.append(f"{symbol}: {action} - {reason}")
        
        return alerts[:3]  # Max 3 urgent alerts
    
    def _get_signal_urgency(self, score: float, confidence: str, timing_data: Dict) -> str:
        """Determine signal urgency"""
        
        timing_urgency = timing_data.get('urgency_level', 'LOW')
        
        if score >= 8 and confidence == 'High' and timing_urgency in ['HIGH', 'CRITICAL']:
            return 'HIGH'
        elif score >= 6 and confidence in ['High', 'Medium'] and timing_urgency == 'HIGH':
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _get_best_strategy(self, strategies: Dict) -> Dict[str, Any]:
        """Get best strategy from multiple strategies"""
        
        valid_strategies = [s for s in strategies.values() if 'error' not in s and s.get('score', 0) > 0]
        
        if not valid_strategies:
            return None
        
        return max(valid_strategies, key=lambda x: x.get('score', 0))
    
    def _convert_signal_to_prediction(self, signal: str) -> str:
        """Convert trading signal to prediction"""
        
        if signal == 'BUY':
            return 'UP'
        elif signal == 'SELL':
            return 'DOWN'
        else:
            return 'SIDEWAYS'
