#!/usr/bin/env python3
"""
Comprehensive Backtesting System for Indian Stock Market Strategies
Tests all strategies on historical data and provides detailed performance reports
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from tools.nlp_processor import NLPProcessor
from tools.indian_market_data import IndianMarketData
import warnings
warnings.filterwarnings('ignore')

class IndianStrategyBacktester:
    """Comprehensive backtesting for Indian market strategies"""
    
    def __init__(self):
        self.nlp_processor = NLPProcessor()
        self.market_data = IndianMarketData()
        
        # Indian stocks for testing
        self.test_stocks = {
            'RELIANCE': 'RELIANCE.NS',
            'INFY': 'INFY.NS', 
            'HDFCBANK': 'HDFCBANK.NS',
            'ICICIBANK': 'ICICIBANK.NS',
            'TCS': 'TCS.NS'
        }
        
        # Strategy parameters
        self.initial_capital = 100000  # ₹1 Lakh
        self.position_size = 0.2  # 20% per trade
        self.commission = 0.001  # 0.1% brokerage
        
        print("🧪 INDIAN STRATEGY BACKTESTER INITIALIZED")
        print("💰 Initial Capital: ₹1,00,000")
        print("📊 Position Size: 20% per trade")
        print("💸 Commission: 0.1%")
    
    def get_historical_data(self, symbol: str, period: str = "3mo") -> pd.DataFrame:
        """Get historical intraday data for backtesting"""
        try:
            yahoo_symbol = self.test_stocks.get(symbol, f"{symbol}.NS")
            
            # Get intraday data
            data = yf.download(yahoo_symbol, period=period, interval="15m", progress=False)
            
            if data.empty:
                print(f"⚠️ No data for {symbol}, generating sample data")
                return self._generate_sample_data(symbol, period)
            
            # Calculate technical indicators
            data = self._calculate_indicators(data)
            
            print(f"✅ {symbol}: {len(data)} data points from {data.index[0].date()} to {data.index[-1].date()}")
            return data
            
        except Exception as e:
            print(f"❌ Error getting data for {symbol}: {e}")
            return self._generate_sample_data(symbol, period)
    
    def _calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for backtesting"""
        df = data.copy()
        
        # Moving averages
        df['EMA_5'] = df['Close'].ewm(span=5).mean()
        df['EMA_20'] = df['Close'].ewm(span=20).mean()
        df['SMA_50'] = df['Close'].rolling(window=50).mean()
        
        # RSI
        df['RSI_14'] = self._calculate_rsi(df['Close'], 14)
        df['RSI_3'] = self._calculate_rsi(df['Close'], 3)
        
        # MACD
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # Bollinger Bands
        bb_middle = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = bb_middle + (bb_std * 2)
        df['BB_Lower'] = bb_middle - (bb_std * 2)
        df['BB_Middle'] = bb_middle
        
        # Volume indicators
        df['Volume_SMA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _generate_sample_data(self, symbol: str, period: str) -> pd.DataFrame:
        """Generate sample data for testing"""
        if period == "3mo":
            periods = 1000
        elif period == "1mo":
            periods = 350
        else:
            periods = 500
        
        # Generate realistic Indian stock data
        base_prices = {
            'RELIANCE': 1500,
            'INFY': 1600,
            'HDFCBANK': 2000,
            'ICICIBANK': 1400,
            'TCS': 3500
        }
        
        base_price = base_prices.get(symbol, 1500)
        
        # Generate timestamps (15-minute intervals, market hours only)
        end_time = datetime.now()
        timestamps = []
        current = end_time - timedelta(days=periods//26)  # Approximate days
        
        while len(timestamps) < periods:
            if current.weekday() < 5:  # Monday to Friday
                market_start = current.replace(hour=9, minute=15, second=0, microsecond=0)
                market_end = current.replace(hour=15, minute=30, second=0, microsecond=0)
                
                time_slot = market_start
                while time_slot <= market_end and len(timestamps) < periods:
                    timestamps.append(time_slot)
                    time_slot += timedelta(minutes=15)
            
            current += timedelta(days=1)
        
        timestamps = timestamps[:periods]
        
        # Generate price data with realistic Indian market characteristics
        returns = np.random.normal(0, 0.015, periods)  # 1.5% volatility
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # Create OHLCV data
        data = []
        for i, (ts, price) in enumerate(zip(timestamps, prices)):
            volatility = abs(np.random.normal(0, 0.008))  # Intraday volatility
            high = price * (1 + volatility)
            low = price * (1 - volatility)
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            volume = np.random.randint(100000, 2000000)  # Indian stock volumes
            
            data.append({
                'Open': open_price,
                'High': max(open_price, high, close_price),
                'Low': min(open_price, low, close_price),
                'Close': close_price,
                'Volume': volume
            })
        
        df = pd.DataFrame(data, index=timestamps)
        return self._calculate_indicators(df)
    
    def strategy_momentum_breakout(self, data: pd.DataFrame) -> pd.DataFrame:
        """Momentum breakout strategy"""
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['position'] = 0
        
        # Strategy logic
        conditions_buy = (
            (data['Close'] > data['EMA_5']) &
            (data['EMA_5'] > data['EMA_20']) &
            (data['Volume_Ratio'] > 1.5) &
            (data['RSI_14'] > 50) &
            (data['RSI_14'] < 80)
        )
        
        conditions_sell = (
            (data['Close'] < data['EMA_5']) |
            (data['RSI_14'] > 80) |
            (data['RSI_14'] < 30)
        )
        
        signals.loc[conditions_buy, 'signal'] = 1
        signals.loc[conditions_sell, 'signal'] = -1
        
        # Generate positions
        signals['position'] = signals['signal'].replace(to_replace=0, method='ffill').fillna(0)
        
        return signals
    
    def strategy_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """Mean reversion strategy using RSI and Bollinger Bands"""
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['position'] = 0
        
        # Strategy logic
        conditions_buy = (
            (data['RSI_3'] < 30) &
            (data['Close'] < data['BB_Lower']) &
            (data['Volume_Ratio'] > 1.2)
        )
        
        conditions_sell = (
            (data['RSI_3'] > 70) &
            (data['Close'] > data['BB_Upper']) &
            (data['Volume_Ratio'] > 1.2)
        )
        
        conditions_exit = (
            (data['Close'] > data['BB_Middle']) |
            (data['RSI_3'] > 50)
        )
        
        signals.loc[conditions_buy, 'signal'] = 1
        signals.loc[conditions_sell, 'signal'] = -1
        signals.loc[conditions_exit, 'signal'] = 0
        
        # Generate positions
        signals['position'] = signals['signal'].replace(to_replace=0, method='ffill').fillna(0)
        
        return signals
    
    def strategy_macd_crossover(self, data: pd.DataFrame) -> pd.DataFrame:
        """MACD crossover strategy"""
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['position'] = 0
        
        # MACD crossover signals
        macd_cross_up = (data['MACD'] > data['MACD_Signal']) & (data['MACD'].shift(1) <= data['MACD_Signal'].shift(1))
        macd_cross_down = (data['MACD'] < data['MACD_Signal']) & (data['MACD'].shift(1) >= data['MACD_Signal'].shift(1))
        
        # Additional filters
        conditions_buy = macd_cross_up & (data['Volume_Ratio'] > 1.1) & (data['Close'] > data['EMA_20'])
        conditions_sell = macd_cross_down | (data['RSI_14'] > 75)
        
        signals.loc[conditions_buy, 'signal'] = 1
        signals.loc[conditions_sell, 'signal'] = -1
        
        # Generate positions
        signals['position'] = signals['signal'].replace(to_replace=0, method='ffill').fillna(0)
        
        return signals
    
    def calculate_performance(self, data: pd.DataFrame, signals: pd.DataFrame, strategy_name: str) -> dict:
        """Calculate strategy performance metrics"""
        # Calculate returns
        data['returns'] = data['Close'].pct_change()
        signals['strategy_returns'] = signals['position'].shift(1) * data['returns']
        
        # Remove NaN values
        strategy_returns = signals['strategy_returns'].dropna()
        
        if len(strategy_returns) == 0:
            return self._empty_performance(strategy_name)
        
        # Performance metrics
        total_return = (1 + strategy_returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(strategy_returns)) - 1
        volatility = strategy_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # Drawdown calculation
        cumulative = (1 + strategy_returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # Trade analysis
        positions = signals['position'].diff()
        trades = positions[positions != 0]
        num_trades = len(trades) // 2  # Buy and sell pairs
        
        # Win rate calculation
        trade_returns = []
        position = 0
        entry_price = 0
        
        for i, pos in enumerate(signals['position']):
            if position == 0 and pos != 0:  # Entry
                position = pos
                entry_price = data['Close'].iloc[i]
            elif position != 0 and pos == 0:  # Exit
                exit_price = data['Close'].iloc[i]
                trade_return = (exit_price - entry_price) / entry_price * position
                trade_returns.append(trade_return)
                position = 0
        
        winning_trades = len([r for r in trade_returns if r > 0])
        win_rate = winning_trades / len(trade_returns) if trade_returns else 0
        
        # Final portfolio value
        final_value = self.initial_capital * (1 + total_return)
        
        return {
            'strategy': strategy_name,
            'total_return': total_return * 100,
            'annual_return': annual_return * 100,
            'volatility': volatility * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown * 100,
            'num_trades': num_trades,
            'win_rate': win_rate * 100,
            'final_value': final_value,
            'profit_loss': final_value - self.initial_capital
        }
    
    def _empty_performance(self, strategy_name: str) -> dict:
        """Return empty performance metrics"""
        return {
            'strategy': strategy_name,
            'total_return': 0,
            'annual_return': 0,
            'volatility': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'num_trades': 0,
            'win_rate': 0,
            'final_value': self.initial_capital,
            'profit_loss': 0
        }
    
    def backtest_stock(self, symbol: str, period: str = "3mo") -> dict:
        """Backtest all strategies on a single stock"""
        print(f"\n📊 BACKTESTING {symbol}")
        print("=" * 50)
        
        # Get historical data
        data = self.get_historical_data(symbol, period)
        
        if data.empty:
            print(f"❌ No data available for {symbol}")
            return {}
        
        strategies = {
            'Momentum Breakout': self.strategy_momentum_breakout,
            'Mean Reversion': self.strategy_mean_reversion,
            'MACD Crossover': self.strategy_macd_crossover
        }
        
        results = {}
        
        for strategy_name, strategy_func in strategies.items():
            print(f"🧪 Testing {strategy_name}...")
            
            try:
                signals = strategy_func(data)
                performance = self.calculate_performance(data, signals, strategy_name)
                results[strategy_name] = performance
                
                print(f"   ✅ Return: {performance['total_return']:.2f}%")
                print(f"   📊 Sharpe: {performance['sharpe_ratio']:.2f}")
                print(f"   📉 Max DD: {performance['max_drawdown']:.2f}%")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                results[strategy_name] = self._empty_performance(strategy_name)
        
        return results
    
    def run_comprehensive_backtest(self) -> dict:
        """Run comprehensive backtest on all stocks"""
        print("🚀 COMPREHENSIVE INDIAN MARKET BACKTEST")
        print("=" * 60)
        
        all_results = {}
        
        for symbol in self.test_stocks.keys():
            stock_results = self.backtest_stock(symbol, "3mo")
            all_results[symbol] = stock_results
        
        return all_results
    
    def generate_report(self, results: dict):
        """Generate comprehensive backtest report"""
        print(f"\n{'='*80}")
        print("📈 COMPREHENSIVE BACKTEST REPORT - INDIAN STOCK MARKET")
        print(f"{'='*80}")
        print(f"📅 Period: Last 3 months")
        print(f"💰 Initial Capital: ₹{self.initial_capital:,}")
        print(f"📊 Position Size: {self.position_size*100}% per trade")
        print(f"💸 Commission: {self.commission*100}%")
        
        # Summary table
        print(f"\n{'='*80}")
        print("📊 STRATEGY PERFORMANCE SUMMARY")
        print(f"{'='*80}")
        
        strategy_summary = {}
        
        for stock, stock_results in results.items():
            print(f"\n🏢 {stock}")
            print("-" * 60)
            print(f"{'Strategy':<20} {'Return%':<10} {'Sharpe':<8} {'MaxDD%':<10} {'Trades':<8} {'WinRate%':<10}")
            print("-" * 60)
            
            for strategy, metrics in stock_results.items():
                if strategy not in strategy_summary:
                    strategy_summary[strategy] = []
                
                strategy_summary[strategy].append(metrics)
                
                print(f"{strategy:<20} {metrics['total_return']:>8.2f}% {metrics['sharpe_ratio']:>7.2f} "
                      f"{metrics['max_drawdown']:>8.2f}% {metrics['num_trades']:>7.0f} {metrics['win_rate']:>8.2f}%")
        
        # Overall strategy performance
        print(f"\n{'='*80}")
        print("🏆 OVERALL STRATEGY RANKINGS")
        print(f"{'='*80}")
        
        strategy_avg = {}
        for strategy, metrics_list in strategy_summary.items():
            avg_return = np.mean([m['total_return'] for m in metrics_list])
            avg_sharpe = np.mean([m['sharpe_ratio'] for m in metrics_list])
            avg_drawdown = np.mean([m['max_drawdown'] for m in metrics_list])
            total_trades = sum([m['num_trades'] for m in metrics_list])
            avg_winrate = np.mean([m['win_rate'] for m in metrics_list])
            
            strategy_avg[strategy] = {
                'avg_return': avg_return,
                'avg_sharpe': avg_sharpe,
                'avg_drawdown': avg_drawdown,
                'total_trades': total_trades,
                'avg_winrate': avg_winrate
            }
        
        # Sort by Sharpe ratio
        sorted_strategies = sorted(strategy_avg.items(), key=lambda x: x[1]['avg_sharpe'], reverse=True)
        
        print(f"{'Rank':<5} {'Strategy':<20} {'Avg Return%':<12} {'Avg Sharpe':<11} {'Avg MaxDD%':<11} {'Total Trades':<12} {'Avg WinRate%':<12}")
        print("-" * 85)
        
        for rank, (strategy, metrics) in enumerate(sorted_strategies, 1):
            emoji = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else "📊"
            print(f"{rank:<5} {strategy:<20} {metrics['avg_return']:>10.2f}% {metrics['avg_sharpe']:>10.2f} "
                  f"{metrics['avg_drawdown']:>9.2f}% {metrics['total_trades']:>11.0f} {metrics['avg_winrate']:>10.2f}% {emoji}")
        
        # Best performing stocks
        print(f"\n{'='*80}")
        print("⭐ BEST PERFORMING STOCKS")
        print(f"{'='*80}")
        
        stock_performance = {}
        for stock, stock_results in results.items():
            best_strategy = max(stock_results.items(), key=lambda x: x[1]['sharpe_ratio'])
            stock_performance[stock] = {
                'best_strategy': best_strategy[0],
                'return': best_strategy[1]['total_return'],
                'sharpe': best_strategy[1]['sharpe_ratio'],
                'final_value': best_strategy[1]['final_value']
            }
        
        sorted_stocks = sorted(stock_performance.items(), key=lambda x: x[1]['sharpe'], reverse=True)
        
        print(f"{'Rank':<5} {'Stock':<12} {'Best Strategy':<20} {'Return%':<10} {'Sharpe':<8} {'Final Value':<12}")
        print("-" * 75)
        
        for rank, (stock, metrics) in enumerate(sorted_stocks, 1):
            emoji = "🚀" if rank == 1 else "📈" if rank == 2 else "📊"
            print(f"{rank:<5} {stock:<12} {metrics['best_strategy']:<20} {metrics['return']:>8.2f}% "
                  f"{metrics['sharpe']:>7.2f} ₹{metrics['final_value']:>10,.0f} {emoji}")
        
        # Recommendations
        print(f"\n{'='*80}")
        print("💡 RECOMMENDATIONS")
        print(f"{'='*80}")
        
        best_overall = sorted_strategies[0]
        best_stock = sorted_stocks[0]
        
        print(f"🏆 BEST STRATEGY: {best_overall[0]}")
        print(f"   📊 Average Return: {best_overall[1]['avg_return']:.2f}%")
        print(f"   📈 Average Sharpe: {best_overall[1]['avg_sharpe']:.2f}")
        print(f"   🛡️ Average Max Drawdown: {best_overall[1]['avg_drawdown']:.2f}%")
        
        print(f"\n🚀 BEST STOCK: {best_stock[0]}")
        print(f"   🎯 Best Strategy: {best_stock[1]['best_strategy']}")
        print(f"   💰 Return: {best_stock[1]['return']:.2f}%")
        print(f"   📊 Sharpe Ratio: {best_stock[1]['sharpe']:.2f}")
        
        print(f"\n🎯 TRADING RECOMMENDATIONS:")
        print(f"   1. Focus on {best_overall[0]} strategy")
        print(f"   2. Prioritize {best_stock[0]} stock")
        print(f"   3. Use ₹{self.initial_capital * self.position_size:,.0f} position size")
        print(f"   4. Monitor drawdown levels closely")
        print(f"   5. Maintain disciplined risk management")
        
        return {
            'best_strategy': best_overall[0],
            'best_stock': best_stock[0],
            'strategy_rankings': sorted_strategies,
            'stock_rankings': sorted_stocks
        }


def main():
    """Main backtesting function"""
    backtester = IndianStrategyBacktester()
    
    # Run comprehensive backtest
    results = backtester.run_comprehensive_backtest()
    
    # Generate detailed report
    report = backtester.generate_report(results)
    
    print(f"\n✅ BACKTEST COMPLETED!")
    print(f"📊 Tested {len(backtester.test_stocks)} stocks with 3 strategies")
    print(f"🎯 Best Strategy: {report['best_strategy']}")
    print(f"🚀 Best Stock: {report['best_stock']}")


if __name__ == "__main__":
    main()
