#!/usr/bin/env python3
"""
Fast Parallel Stock Analyzer
Ultra-fast parallel processing to analyze hundreds of stocks in under 1 minute
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import requests
from typing import List, Dict, Tuple
import queue
import numpy as np
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.equity_csv_stocks_fetcher import EquityCSVStocksFetcher

class FastParallelAnalyzer:
    """Ultra-fast parallel stock analyzer with backtesting capabilities"""

    def __init__(self, max_workers=50, batch_size=20, enable_backtesting=False):
        self.max_workers = max_workers  # Number of parallel threads
        self.batch_size = batch_size    # Stocks per batch
        self.enable_backtesting = enable_backtesting  # Enable comprehensive backtesting
        self.results_lock = Lock()      # Thread-safe results
        self.results = []
        self.backtest_results = []      # Store backtesting results
        self.processed_count = 0

        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        print(f"🚀 Fast Parallel Analyzer initialized")
        print(f"   ⚡ Max Workers: {max_workers}")
        print(f"   📦 Batch Size: {batch_size}")
        print(f"   🔍 Backtesting: {'Enabled' if enable_backtesting else 'Disabled'}")
    
    def get_stock_data_batch(self, symbols_batch: List[str]) -> List[Dict]:
        """Get stock data for a batch of symbols in parallel"""
        
        batch_results = []
        
        # Use ThreadPoolExecutor for the batch
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit all stocks in the batch
            future_to_symbol = {
                executor.submit(self.get_single_stock_data, symbol): symbol 
                for symbol in symbols_batch
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_symbol, timeout=30):
                symbol = future_to_symbol[future]
                try:
                    result = future.result(timeout=5)
                    if result:
                        batch_results.append(result)
                except Exception as e:
                    # Skip failed stocks to maintain speed
                    pass
        
        return batch_results
    
    def get_single_stock_data(self, symbol: str) -> Dict:
        """Get data for a single stock (optimized for speed)"""
        
        try:
            # Use Yahoo Finance API for speed
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}.NS"
            
            response = self.session.get(url, timeout=3)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'chart' in data and data['chart']['result']:
                    result = data['chart']['result'][0]
                    meta = result.get('meta', {})
                    
                    # Extract key data
                    current_price = meta.get('regularMarketPrice', 0)
                    prev_close = meta.get('previousClose', current_price)
                    volume = meta.get('regularMarketVolume', 0)
                    
                    # Calculate change
                    change = current_price - prev_close
                    change_pct = (change / prev_close * 100) if prev_close > 0 else 0
                    
                    # Calculate trading score quickly
                    score = self.calculate_fast_score(symbol, current_price, change_pct, volume)

                    # Calculate trading levels
                    trading_levels = self.calculate_trading_levels(current_price, change_pct, volume, symbol)

                    return {
                        'symbol': symbol,
                        'price': round(current_price, 2),
                        'change': round(change, 2),
                        'change_percent': round(change_pct, 2),
                        'volume': volume,
                        'score': round(score, 1),
                        'recommendation': self.get_fast_recommendation(score, change_pct),
                        'buy_price': trading_levels['buy_price'],
                        'sell_target_1': trading_levels['sell_target_1'],
                        'sell_target_2': trading_levels['sell_target_2'],
                        'stop_loss': trading_levels['stop_loss'],
                        'expected_change_pct': trading_levels['expected_change_pct']
                    }
            
            return None
            
        except Exception:
            return None
    
    def calculate_fast_score(self, symbol: str, price: float, change_pct: float, volume: int) -> float:
        """Fast scoring algorithm optimized for BUY signals"""

        score = 0.0  # Start from 0 for better differentiation

        # BUY SIGNAL FOCUSED SCORING

        # 1. Positive momentum gets highest priority
        if change_pct > 5:
            score += 40  # Exceptional momentum
        elif change_pct > 3:
            score += 35  # Strong momentum
        elif change_pct > 2:
            score += 30  # Good momentum
        elif change_pct > 1:
            score += 25  # Mild momentum
        elif change_pct > 0.5:
            score += 15  # Slight momentum
        elif change_pct > 0:
            score += 10  # Positive but weak
        else:
            score += 0   # No points for negative momentum

        # 2. Volume factor (liquidity for execution)
        if volume > 10000000:
            score += 25  # Excellent liquidity
        elif volume > 5000000:
            score += 20  # Very good liquidity
        elif volume > 1000000:
            score += 15  # Good liquidity
        elif volume > 500000:
            score += 10  # Decent liquidity
        elif volume > 100000:
            score += 5   # Minimal liquidity
        else:
            score += 0   # Poor liquidity

        # 3. Price range factor (tradeable range)
        if 100 <= price <= 2000:
            score += 20  # Optimal trading range
        elif 50 <= price <= 100 or 2000 <= price <= 5000:
            score += 15  # Good trading range
        elif 20 <= price <= 50 or 5000 <= price <= 10000:
            score += 10  # Acceptable range
        elif price < 20:
            score += 0   # Penny stock - avoid
        elif price > 10000:
            score += 5   # Very expensive but tradeable

        # 4. Blue-chip/Liquid stock bonus
        liquid_symbols = {
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFY', 'HINDUNILVR', 'ITC', 'SBIN',
            'BHARTIARTL', 'KOTAKBANK', 'ASIANPAINT', 'LT', 'AXISBANK', 'MARUTI', 'SUNPHARMA',
            'TITAN', 'ULTRACEMCO', 'NESTLEIND', 'WIPRO', 'HCLTECH', 'BAJFINANCE', 'POWERGRID',
            'NTPC', 'TECHM', 'ONGC', 'TATAMOTORS', 'TATASTEEL', 'ADANIENT', 'APOLLOHOSP', 'CIPLA'
        }

        if symbol in liquid_symbols:
            score += 15  # Blue-chip bonus

        return min(score, 100.0)

    def calculate_trading_levels(self, current_price: float, change_pct: float, volume: int, symbol: str) -> Dict:
        """Calculate detailed trading levels for buy/sell decisions"""

        # Determine volatility factor based on current movement and volume
        volatility_factor = 1.0

        # Adjust volatility based on current change
        if abs(change_pct) > 5:
            volatility_factor = 1.5  # High volatility
        elif abs(change_pct) > 3:
            volatility_factor = 1.3  # Medium-high volatility
        elif abs(change_pct) > 1:
            volatility_factor = 1.1  # Medium volatility
        else:
            volatility_factor = 0.8  # Low volatility

        # Adjust for volume (higher volume = more reliable levels)
        volume_factor = 1.0
        if volume > 10000000:
            volume_factor = 0.8  # Tighter levels for high volume
        elif volume > 1000000:
            volume_factor = 0.9  # Slightly tighter
        elif volume < 100000:
            volume_factor = 1.2  # Wider levels for low volume

        # Base percentage levels
        base_stop_loss_pct = 2.0 * volatility_factor * volume_factor
        base_target_1_pct = 3.0 * volatility_factor * volume_factor
        base_target_2_pct = 5.0 * volatility_factor * volume_factor

        # Adjust buy price based on current momentum
        if change_pct > 2:
            # Strong upward momentum - buy at current price or slight premium
            buy_price = current_price * 1.002  # 0.2% premium for momentum
        elif change_pct > 0:
            # Positive momentum - buy at current price
            buy_price = current_price
        else:
            # Negative or flat - wait for slight dip
            buy_price = current_price * 0.998  # 0.2% discount

        # Calculate levels
        stop_loss = buy_price * (1 - base_stop_loss_pct / 100)
        sell_target_1 = buy_price * (1 + base_target_1_pct / 100)
        sell_target_2 = buy_price * (1 + base_target_2_pct / 100)

        # Calculate expected change percentage for the day
        expected_change_pct = self.calculate_expected_change(change_pct, volume, symbol)

        return {
            'buy_price': round(buy_price, 2),
            'sell_target_1': round(sell_target_1, 2),
            'sell_target_2': round(sell_target_2, 2),
            'stop_loss': round(stop_loss, 2),
            'expected_change_pct': round(expected_change_pct, 2)
        }

    def calculate_expected_change(self, current_change_pct: float, volume: int, symbol: str) -> float:
        """Calculate expected change percentage for the day"""

        # Base expectation on current momentum
        base_expectation = current_change_pct

        # Momentum continuation factor
        momentum_factor = 1.0
        if abs(current_change_pct) > 3:
            momentum_factor = 0.7  # Strong moves tend to moderate
        elif abs(current_change_pct) > 1:
            momentum_factor = 0.8  # Medium moves continue partially
        elif abs(current_change_pct) > 0.5:
            momentum_factor = 0.9  # Small moves may continue
        else:
            momentum_factor = 1.2  # Flat stocks may move

        # Volume impact
        volume_boost = 0
        if volume > 10000000:
            volume_boost = 0.5  # High volume adds momentum
        elif volume > 5000000:
            volume_boost = 0.3
        elif volume > 1000000:
            volume_boost = 0.1

        # Market time factor (simulated - in real implementation, use actual market time)
        import datetime
        current_hour = datetime.datetime.now().hour

        time_factor = 1.0
        if 9 <= current_hour <= 11:  # Morning session - higher volatility
            time_factor = 1.2
        elif 14 <= current_hour <= 15:  # Afternoon session - moderate volatility
            time_factor = 1.1
        else:  # Off-market hours - conservative estimate
            time_factor = 0.8

        # Calculate expected change
        expected_change = (base_expectation * momentum_factor + volume_boost) * time_factor

        # Apply realistic bounds
        if current_change_pct > 0:
            # For positive stocks, expect continued but moderated gains
            expected_change = max(0.2, min(expected_change, current_change_pct + 2.0))
        else:
            # For negative stocks, expect potential recovery or continued decline
            expected_change = max(current_change_pct - 1.0, min(expected_change, 1.0))

        return expected_change

    def get_historical_data(self, symbol: str, days: int = 30) -> Dict:
        """Get historical data for backtesting"""

        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Convert to timestamps
            start_timestamp = int(start_date.timestamp())
            end_timestamp = int(end_date.timestamp())

            # Yahoo Finance historical data API
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}.NS"
            params = {
                'period1': start_timestamp,
                'period2': end_timestamp,
                'interval': '1d',
                'includePrePost': 'true'
            }

            response = self.session.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                if 'chart' in data and data['chart']['result']:
                    result = data['chart']['result'][0]

                    # Extract historical data
                    timestamps = result.get('timestamp', [])
                    quotes = result.get('indicators', {}).get('quote', [{}])[0]

                    opens = quotes.get('open', [])
                    highs = quotes.get('high', [])
                    lows = quotes.get('low', [])
                    closes = quotes.get('close', [])
                    volumes = quotes.get('volume', [])

                    # Create historical data structure
                    historical_data = []
                    for i in range(len(timestamps)):
                        if all(x is not None for x in [opens[i], highs[i], lows[i], closes[i], volumes[i]]):
                            historical_data.append({
                                'date': datetime.fromtimestamp(timestamps[i]).strftime('%Y-%m-%d'),
                                'open': opens[i],
                                'high': highs[i],
                                'low': lows[i],
                                'close': closes[i],
                                'volume': volumes[i]
                            })

                    return {
                        'symbol': symbol,
                        'data': historical_data,
                        'days_available': len(historical_data)
                    }

            return None

        except Exception:
            return None

    def analyze_stock_behavior(self, symbol: str, historical_data: List[Dict]) -> Dict:
        """Analyze stock behavior patterns from historical data"""

        if len(historical_data) < 5:
            return {'symbol': symbol, 'analysis': 'insufficient_data'}

        try:
            # Convert to pandas for easier analysis
            df = pd.DataFrame(historical_data)
            df['close'] = pd.to_numeric(df['close'])
            df['volume'] = pd.to_numeric(df['volume'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])

            # Calculate daily returns
            df['daily_return'] = df['close'].pct_change() * 100
            df['volatility'] = df['daily_return'].rolling(window=5).std()

            # Calculate technical indicators
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_10'] = df['close'].rolling(window=min(10, len(df))).mean()

            # Volume analysis
            df['volume_sma'] = df['volume'].rolling(window=5).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']

            # Trend analysis
            recent_trend = 'neutral'
            if len(df) >= 5:
                recent_closes = df['close'].tail(5).values
                if recent_closes[-1] > recent_closes[0] * 1.02:
                    recent_trend = 'bullish'
                elif recent_closes[-1] < recent_closes[0] * 0.98:
                    recent_trend = 'bearish'

            # Support and resistance levels
            support_level = df['low'].rolling(window=min(10, len(df))).min().iloc[-1]
            resistance_level = df['high'].rolling(window=min(10, len(df))).max().iloc[-1]

            # Behavioral patterns
            avg_daily_return = df['daily_return'].mean()
            volatility_score = df['volatility'].mean()
            volume_consistency = 1 / (df['volume_ratio'].std() + 0.1)  # Higher = more consistent

            # Win rate calculation (positive days)
            positive_days = len(df[df['daily_return'] > 0])
            win_rate = (positive_days / len(df)) * 100

            # Momentum score
            momentum_score = 0
            if len(df) >= 3:
                recent_returns = df['daily_return'].tail(3).mean()
                if recent_returns > 1:
                    momentum_score = min(recent_returns * 10, 100)
                elif recent_returns > 0:
                    momentum_score = recent_returns * 5

            # Risk assessment
            max_drawdown = 0
            peak = df['close'].iloc[0]
            for price in df['close']:
                if price > peak:
                    peak = price
                drawdown = (peak - price) / peak * 100
                max_drawdown = max(max_drawdown, drawdown)

            return {
                'symbol': symbol,
                'analysis': 'complete',
                'metrics': {
                    'avg_daily_return': round(avg_daily_return, 2),
                    'volatility_score': round(volatility_score, 2),
                    'win_rate': round(win_rate, 1),
                    'momentum_score': round(momentum_score, 1),
                    'volume_consistency': round(volume_consistency, 2),
                    'max_drawdown': round(max_drawdown, 2),
                    'recent_trend': recent_trend,
                    'support_level': round(support_level, 2),
                    'resistance_level': round(resistance_level, 2),
                    'days_analyzed': len(df)
                },
                'prediction_confidence': self.calculate_prediction_confidence(df),
                'trading_recommendation': self.generate_trading_recommendation(df, recent_trend, momentum_score, win_rate)
            }

        except Exception as e:
            return {'symbol': symbol, 'analysis': 'error', 'error': str(e)}

    def calculate_prediction_confidence(self, df: pd.DataFrame) -> float:
        """Calculate confidence level for predictions based on historical patterns"""

        confidence = 50.0  # Base confidence

        # Volume consistency adds confidence
        volume_std = df['volume'].std() / df['volume'].mean()
        if volume_std < 0.5:
            confidence += 15
        elif volume_std < 1.0:
            confidence += 10

        # Trend consistency
        trend_changes = 0
        for i in range(1, len(df)):
            if (df['daily_return'].iloc[i] > 0) != (df['daily_return'].iloc[i-1] > 0):
                trend_changes += 1

        trend_consistency = 1 - (trend_changes / len(df))
        confidence += trend_consistency * 20

        # Data availability
        if len(df) >= 20:
            confidence += 15
        elif len(df) >= 10:
            confidence += 10

        return min(confidence, 100.0)

    def generate_trading_recommendation(self, df: pd.DataFrame, trend: str, momentum: float, win_rate: float) -> str:
        """Generate enhanced trading recommendation based on backtesting"""

        # Enhanced recommendation logic
        if trend == 'bullish' and momentum > 20 and win_rate > 60:
            return 'STRONG BUY'
        elif trend == 'bullish' and (momentum > 10 or win_rate > 55):
            return 'BUY'
        elif trend == 'bearish' and momentum < -20 and win_rate < 40:
            return 'STRONG SELL'
        elif trend == 'bearish' and (momentum < -10 or win_rate < 45):
            return 'SELL'
        elif win_rate > 50 and momentum > 5:
            return 'MODERATE BUY'
        else:
            return 'HOLD'
    
    def get_fast_recommendation(self, score: float, change_pct: float) -> str:
        """Fast recommendation focused on BUY signals only"""

        # Only recommend BUY for positive momentum stocks
        if change_pct > 0:
            if score >= 80:
                return "STRONG BUY"
            elif score >= 65:
                return "BUY"
            elif score >= 50:
                return "MODERATE BUY"
            elif score >= 35:
                return "WEAK BUY"
            else:
                return "HOLD"
        else:
            # No buy recommendations for negative momentum
            return "AVOID"
    
    def process_batch_with_backtesting(self, batch_symbols: List[str], batch_num: int, total_batches: int) -> Tuple[List[Dict], List[Dict]]:
        """Process a batch of stocks with comprehensive backtesting"""

        start_time = time.time()

        print(f"🔍 Processing batch {batch_num}/{total_batches} ({len(batch_symbols)} stocks) with backtesting...")

        # Get current data for the entire batch in parallel
        batch_results = self.get_stock_data_batch(batch_symbols)
        batch_backtest_results = []

        if self.enable_backtesting:
            # Get historical data and analyze behavior for each stock
            with ThreadPoolExecutor(max_workers=10) as executor:
                # Submit historical data requests
                future_to_symbol = {
                    executor.submit(self.get_historical_data, symbol, 30): symbol
                    for symbol in batch_symbols
                }

                # Process historical data as it becomes available
                for future in as_completed(future_to_symbol, timeout=60):
                    symbol = future_to_symbol[future]
                    try:
                        historical_result = future.result(timeout=10)
                        if historical_result and historical_result.get('data'):
                            # Analyze behavior patterns
                            behavior_analysis = self.analyze_stock_behavior(
                                symbol,
                                historical_result['data']
                            )
                            batch_backtest_results.append(behavior_analysis)
                    except Exception:
                        # Skip failed backtests to maintain speed
                        pass

        # Update global results thread-safely
        with self.results_lock:
            self.results.extend(batch_results)
            self.backtest_results.extend(batch_backtest_results)
            self.processed_count += len(batch_symbols)

        elapsed = time.time() - start_time
        backtest_count = len(batch_backtest_results) if self.enable_backtesting else 0
        print(f"   ✅ Batch {batch_num} completed in {elapsed:.1f}s ({len(batch_results)} current, {backtest_count} backtested)")

        return batch_results, batch_backtest_results

    def process_batch(self, batch_symbols: List[str], batch_num: int, total_batches: int) -> List[Dict]:
        """Process a batch of stocks (legacy method for compatibility)"""

        if self.enable_backtesting:
            batch_results, _ = self.process_batch_with_backtesting(batch_symbols, batch_num, total_batches)
            return batch_results
        else:
            start_time = time.time()

            print(f"⚡ Processing batch {batch_num}/{total_batches} ({len(batch_symbols)} stocks)...")

            # Get data for the entire batch in parallel
            batch_results = self.get_stock_data_batch(batch_symbols)

            # Update global results thread-safely
            with self.results_lock:
                self.results.extend(batch_results)
                self.processed_count += len(batch_symbols)

            elapsed = time.time() - start_time
            print(f"   ✅ Batch {batch_num} completed in {elapsed:.1f}s ({len(batch_results)} successful)")

            return batch_results
    
    def analyze_stocks_parallel(self, symbols: List[str]) -> List[Dict]:
        """Analyze stocks using parallel batch processing"""
        
        total_stocks = len(symbols)
        print(f"\n🚀 STARTING PARALLEL ANALYSIS")
        print(f"📊 Total Stocks: {total_stocks}")
        print(f"⚡ Max Workers: {self.max_workers}")
        print(f"📦 Batch Size: {self.batch_size}")
        print("=" * 60)
        
        start_time = time.time()
        
        # Split stocks into batches
        batches = [symbols[i:i + self.batch_size] for i in range(0, len(symbols), self.batch_size)]
        total_batches = len(batches)
        
        print(f"📦 Created {total_batches} batches")
        
        # Process batches in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(self.process_batch, batch, i+1, total_batches): i 
                for i, batch in enumerate(batches)
            }
            
            # Wait for all batches to complete
            completed_batches = 0
            for future in as_completed(future_to_batch):
                completed_batches += 1
                progress = (completed_batches / total_batches) * 100
                print(f"📈 Progress: {progress:.1f}% ({completed_batches}/{total_batches} batches)")
        
        total_time = time.time() - start_time
        successful_results = len(self.results)
        
        print(f"\n✅ PARALLEL ANALYSIS COMPLETED!")
        print(f"⏱️  Total Time: {total_time:.1f} seconds")
        print(f"📊 Stocks Processed: {self.processed_count}")
        print(f"✅ Successful Results: {successful_results}")
        print(f"⚡ Speed: {successful_results/total_time:.1f} stocks/second")
        print(f"📈 Success Rate: {(successful_results/total_stocks)*100:.1f}%")
        
        return self.results
    
    def enhance_stocks_with_backtesting(self, stocks: List[Dict]) -> List[Dict]:
        """Enhance stock data with backtesting insights"""

        if not self.enable_backtesting or not self.backtest_results:
            return stocks

        # Create lookup dictionary for backtesting results
        backtest_lookup = {bt['symbol']: bt for bt in self.backtest_results if bt.get('analysis') == 'complete'}

        enhanced_stocks = []
        for stock in stocks:
            symbol = stock['symbol']
            enhanced_stock = stock.copy()

            if symbol in backtest_lookup:
                backtest_data = backtest_lookup[symbol]
                metrics = backtest_data.get('metrics', {})

                # Add backtesting metrics
                enhanced_stock['backtest_metrics'] = metrics
                enhanced_stock['prediction_confidence'] = backtest_data.get('prediction_confidence', 50)
                enhanced_stock['historical_recommendation'] = backtest_data.get('trading_recommendation', 'HOLD')

                # Enhance trading levels with historical data
                enhanced_stock = self.enhance_trading_levels_with_backtest(enhanced_stock, metrics)

                # Enhance scoring with backtesting data
                backtest_bonus = 0

                # Win rate bonus
                win_rate = metrics.get('win_rate', 50)
                if win_rate > 70:
                    backtest_bonus += 20
                elif win_rate > 60:
                    backtest_bonus += 15
                elif win_rate > 55:
                    backtest_bonus += 10

                # Momentum consistency bonus
                momentum_score = metrics.get('momentum_score', 0)
                if momentum_score > 30:
                    backtest_bonus += 15
                elif momentum_score > 20:
                    backtest_bonus += 10
                elif momentum_score > 10:
                    backtest_bonus += 5

                # Volume consistency bonus
                volume_consistency = metrics.get('volume_consistency', 1)
                if volume_consistency > 2:
                    backtest_bonus += 10
                elif volume_consistency > 1.5:
                    backtest_bonus += 5

                # Trend alignment bonus
                recent_trend = metrics.get('recent_trend', 'neutral')
                if recent_trend == 'bullish' and stock['change_percent'] > 0:
                    backtest_bonus += 15
                elif recent_trend == 'bearish' and stock['change_percent'] < 0:
                    backtest_bonus += 10

                # Update score with backtesting insights
                enhanced_stock['enhanced_score'] = min(stock['score'] + backtest_bonus, 100)
                enhanced_stock['backtest_bonus'] = backtest_bonus
            else:
                enhanced_stock['enhanced_score'] = stock['score']
                enhanced_stock['backtest_bonus'] = 0
                enhanced_stock['prediction_confidence'] = 50

            enhanced_stocks.append(enhanced_stock)

        return enhanced_stocks

    def enhance_trading_levels_with_backtest(self, stock: Dict, metrics: Dict) -> Dict:
        """Enhance trading levels using backtesting insights"""

        # Get current trading levels
        buy_price = stock.get('buy_price', stock['price'])
        current_stop_loss = stock.get('stop_loss', buy_price * 0.98)
        current_target_1 = stock.get('sell_target_1', buy_price * 1.03)
        current_target_2 = stock.get('sell_target_2', buy_price * 1.05)
        current_expected = stock.get('expected_change_pct', 1.0)

        # Historical volatility adjustment
        volatility_score = metrics.get('volatility_score', 2.0)
        if volatility_score > 3:
            # High volatility - wider stops and targets
            volatility_multiplier = 1.3
        elif volatility_score > 2:
            volatility_multiplier = 1.1
        else:
            # Low volatility - tighter levels
            volatility_multiplier = 0.9

        # Win rate adjustment
        win_rate = metrics.get('win_rate', 50)
        if win_rate > 65:
            # High win rate - more aggressive targets
            win_rate_multiplier = 1.2
        elif win_rate > 55:
            win_rate_multiplier = 1.1
        else:
            # Lower win rate - more conservative
            win_rate_multiplier = 0.9

        # Average daily return adjustment
        avg_return = metrics.get('avg_daily_return', 0)
        if avg_return > 1:
            return_multiplier = 1.3
        elif avg_return > 0.5:
            return_multiplier = 1.1
        elif avg_return < -0.5:
            return_multiplier = 0.8
        else:
            return_multiplier = 1.0

        # Max drawdown adjustment (risk management)
        max_drawdown = metrics.get('max_drawdown', 5)
        if max_drawdown > 10:
            # High risk - tighter stop loss
            risk_multiplier = 0.8
        elif max_drawdown > 5:
            risk_multiplier = 0.9
        else:
            # Low risk - can afford wider stops
            risk_multiplier = 1.1

        # Calculate enhanced levels
        stop_loss_adjustment = volatility_multiplier * risk_multiplier
        target_adjustment = volatility_multiplier * win_rate_multiplier * return_multiplier

        # Apply adjustments
        enhanced_stop_loss = buy_price - (buy_price - current_stop_loss) * stop_loss_adjustment
        enhanced_target_1 = buy_price + (current_target_1 - buy_price) * target_adjustment
        enhanced_target_2 = buy_price + (current_target_2 - buy_price) * target_adjustment * 1.2

        # Enhanced expected change based on historical patterns
        momentum_score = metrics.get('momentum_score', 0)
        recent_trend = metrics.get('recent_trend', 'neutral')

        if recent_trend == 'bullish' and stock['change_percent'] > 0:
            trend_boost = 1.5
        elif recent_trend == 'bearish' and stock['change_percent'] < 0:
            trend_boost = 0.7
        else:
            trend_boost = 1.0

        enhanced_expected = current_expected * return_multiplier * trend_boost

        # Update stock with enhanced levels
        stock['enhanced_buy_price'] = round(buy_price, 2)
        stock['enhanced_stop_loss'] = round(max(enhanced_stop_loss, buy_price * 0.95), 2)  # Max 5% stop loss
        stock['enhanced_sell_target_1'] = round(enhanced_target_1, 2)
        stock['enhanced_sell_target_2'] = round(enhanced_target_2, 2)
        stock['enhanced_expected_change'] = round(enhanced_expected, 2)

        # Add risk-reward ratio
        potential_gain_1 = (enhanced_target_1 - buy_price) / buy_price * 100
        potential_loss = (buy_price - enhanced_stop_loss) / buy_price * 100
        risk_reward_ratio = potential_gain_1 / max(potential_loss, 0.1)
        stock['risk_reward_ratio'] = round(risk_reward_ratio, 2)

        return stock

    def get_top_buy_stocks(self, results: List[Dict], top_n: int = 10) -> List[Dict]:
        """Get top BUY stocks sorted by recommendation priority and enhanced scoring"""

        # Enhance stocks with backtesting data
        enhanced_results = self.enhance_stocks_with_backtesting(results)

        # Filter for BUY signals only (positive momentum)
        buy_stocks = [stock for stock in enhanced_results if stock['change_percent'] > 0 and 'BUY' in stock['recommendation']]

        # Calculate enhanced combined score
        for stock in buy_stocks:
            base_score = stock.get('enhanced_score', stock['score'])
            momentum_factor = stock['change_percent'] * 10
            confidence_factor = stock.get('prediction_confidence', 50) * 0.2

            stock['combined_score'] = (base_score * 0.6) + momentum_factor + confidence_factor

        # Define recommendation priority (higher number = higher priority)
        recommendation_priority = {
            'STRONG BUY': 4,
            'BUY': 3,
            'MODERATE BUY': 2,
            'WEAK BUY': 1
        }

        # Add priority score for sorting
        for stock in buy_stocks:
            stock['priority'] = recommendation_priority.get(stock['recommendation'], 0)

        # Sort by: 1) Recommendation priority (desc), 2) Combined score (desc), 3) Enhanced score (desc)
        sorted_buy_stocks = sorted(buy_stocks, key=lambda x: (x['priority'], x['combined_score'], x.get('enhanced_score', x['score'])), reverse=True)

        return sorted_buy_stocks[:top_n]
    
    def display_top_buy_stocks(self, top_stocks: List[Dict]):
        """Display top BUY stocks with comprehensive trading information"""

        print(f"\n🏆 TOP {len(top_stocks)} BEST BUY STOCKS - COMPREHENSIVE TRADING GUIDE")
        print("=" * 140)

        # Main summary table
        print(f"{'Rank':<4} {'Symbol':<12} {'Current':<10} {'Change%':<8} {'Volume':<12} {'Expected%':<10} {'Confidence':<10} {'Recommendation':<12}")
        print("-" * 140)

        for i, stock in enumerate(top_stocks, 1):
            symbol = stock['symbol']
            price = stock['price']
            change_pct = stock['change_percent']
            volume = stock['volume']
            expected_change = stock.get('enhanced_expected_change', stock.get('expected_change_pct', 0))
            confidence = stock.get('prediction_confidence', 50)
            recommendation = stock['recommendation']

            # Format volume with explanation
            if volume > 1000000:
                vol_str = f"{volume/1000000:.1f}M"
                vol_desc = "High Liquidity"
            elif volume > 100000:
                vol_str = f"{volume/1000:.1f}K"
                vol_desc = "Good Liquidity"
            else:
                vol_str = str(volume)
                vol_desc = "Low Liquidity"

            # Add emoji based on recommendation strength
            if recommendation == "STRONG BUY":
                rec_emoji = "🚀"
            elif recommendation == "BUY":
                rec_emoji = "📈"
            elif recommendation == "MODERATE BUY":
                rec_emoji = "📊"
            else:
                rec_emoji = "🟢"

            print(f"{i:<4} {symbol:<12} ₹{price:<9.2f} {change_pct:>+6.2f}% 🟢 {vol_str:<11} {expected_change:>+8.2f}% {confidence:<10.1f}% {rec_emoji} {recommendation}")

        # Detailed trading levels for top 5 stocks
        print(f"\n📊 DETAILED TRADING LEVELS (TOP 5 STOCKS)")
        print("=" * 140)
        print(f"{'Symbol':<12} {'Buy Price':<10} {'Stop Loss':<10} {'Target 1':<10} {'Target 2':<10} {'Risk:Reward':<12} {'Expected':<10}")
        print("-" * 140)

        for i, stock in enumerate(top_stocks[:5], 1):
            symbol = stock['symbol']
            buy_price = stock.get('enhanced_buy_price', stock.get('buy_price', stock['price']))
            stop_loss = stock.get('enhanced_stop_loss', stock.get('stop_loss', buy_price * 0.98))
            target_1 = stock.get('enhanced_sell_target_1', stock.get('sell_target_1', buy_price * 1.03))
            target_2 = stock.get('enhanced_sell_target_2', stock.get('sell_target_2', buy_price * 1.05))
            risk_reward = stock.get('risk_reward_ratio', 1.5)
            expected_change = stock.get('enhanced_expected_change', stock.get('expected_change_pct', 0))

            print(f"{symbol:<12} ₹{buy_price:<9.2f} ₹{stop_loss:<9.2f} ₹{target_1:<9.2f} ₹{target_2:<9.2f} {risk_reward:<12.2f} {expected_change:>+8.2f}%")

        # Market context explanation
        print(f"\n📋 TRADING INFORMATION EXPLAINED:")
        print(f"=" * 60)
        print(f"📊 Change%: Current day's price movement from previous close")
        print(f"💧 Volume: Trading volume indicating liquidity")
        print(f"   • >1M = High liquidity (easy entry/exit)")
        print(f"   • 100K-1M = Good liquidity (normal trading)")
        print(f"   • <100K = Low liquidity (careful execution)")
        print(f"🎯 Expected%: Predicted change by end of trading day")
        print(f"💰 Buy Price: Recommended entry price")
        print(f"🛡️ Stop Loss: Maximum loss limit (risk management)")
        print(f"🎯 Target 1: First profit booking level (conservative)")
        print(f"🚀 Target 2: Second profit booking level (aggressive)")
        print(f"⚖️ Risk:Reward: Profit potential vs loss risk ratio")

        # Market timing recommendations
        import datetime
        current_hour = datetime.datetime.now().hour
        current_minute = datetime.datetime.now().minute

        print(f"\n⏰ OPTIMAL TRADING TIMES:")
        print(f"=" * 40)

        if 9 <= current_hour < 16:  # Market hours
            print(f"🟢 LIVE MARKET - Current Time: {current_hour:02d}:{current_minute:02d}")
            if 9 <= current_hour <= 10:
                print(f"   🌅 MORNING SESSION: High volatility, good for momentum trades")
            elif 11 <= current_hour <= 13:
                print(f"   ☀️ MID-DAY SESSION: Moderate volatility, trend continuation")
            elif 14 <= current_hour <= 15:
                print(f"   🌆 AFTERNOON SESSION: Increased activity, position adjustments")
            else:
                print(f"   🌙 CLOSING HOUR: Final positioning, avoid new entries")
        else:
            print(f"🔴 OFFLINE MARKET - Prepare for next session")
            print(f"   📋 Review analysis and prepare watchlists")
            print(f"   🎯 Set alerts for buy prices and key levels")
            print(f"   📊 Expected changes are for next trading day")

        print(f"\n🎯 BEST ENTRY TIMES:")
        print(f"   • 09:15-09:30 AM: Opening volatility (momentum plays)")
        print(f"   • 10:15-10:30 AM: Golden hour (best execution)")
        print(f"   • 02:15-02:30 PM: Afternoon momentum")

        # Display backtesting summary if enabled
        if self.enable_backtesting and top_stocks:
            print(f"\n🔍 BACKTESTING INSIGHTS:")
            print(f"=" * 40)

            backtested_count = len([s for s in top_stocks if s.get('backtest_metrics')])
            print(f"📊 Stocks with Historical Analysis: {backtested_count}/{len(top_stocks)}")

            if backtested_count > 0:
                avg_confidence = sum(s.get('prediction_confidence', 50) for s in top_stocks) / len(top_stocks)
                avg_win_rate = sum(s.get('backtest_metrics', {}).get('win_rate', 50) for s in top_stocks if s.get('backtest_metrics')) / max(backtested_count, 1)
                avg_risk_reward = sum(s.get('risk_reward_ratio', 1.5) for s in top_stocks) / len(top_stocks)

                print(f"📈 Average Prediction Confidence: {avg_confidence:.1f}%")
                print(f"🎯 Average Historical Win Rate: {avg_win_rate:.1f}%")
                print(f"⚖️ Average Risk:Reward Ratio: {avg_risk_reward:.2f}")

                # Show top performer's detailed metrics
                top_performer = top_stocks[0]
                if top_performer.get('backtest_metrics'):
                    metrics = top_performer['backtest_metrics']
                    print(f"\n🥇 TOP PERFORMER DETAILED ANALYSIS ({top_performer['symbol']}):")
                    print(f"   📊 Historical Win Rate: {metrics.get('win_rate', 0):.1f}%")
                    print(f"   📈 Average Daily Return: {metrics.get('avg_daily_return', 0):.2f}%")
                    print(f"   🎯 Momentum Score: {metrics.get('momentum_score', 0):.1f}")
                    print(f"   📉 Maximum Drawdown: {metrics.get('max_drawdown', 0):.1f}%")
                    print(f"   📊 Recent Trend: {metrics.get('recent_trend', 'neutral').upper()}")
                    print(f"   💰 Recommended Buy: ₹{top_performer.get('enhanced_buy_price', top_performer['price'])}")
                    print(f"   🛡️ Stop Loss: ₹{top_performer.get('enhanced_stop_loss', 0)}")
                    print(f"   🎯 Target 1: ₹{top_performer.get('enhanced_sell_target_1', 0)}")
                    print(f"   🚀 Target 2: ₹{top_performer.get('enhanced_sell_target_2', 0)}")
                    print(f"   ⚖️ Risk:Reward: {top_performer.get('risk_reward_ratio', 0):.2f}")
                    print(f"   🔮 Expected Change: {top_performer.get('enhanced_expected_change', 0):+.2f}%")

def main():
    """Main function for fast parallel analysis"""
    
    print("🇮🇳 ULTRA-FAST PARALLEL INDIAN STOCK ANALYZER")
    print("⚡ Optimized for Speed - Target: <1 minute analysis")
    print("=" * 70)
    
    try:
        # Initialize analyzer with backtesting enabled
        print("🔍 Initializing with comprehensive backtesting...")
        analyzer = FastParallelAnalyzer(max_workers=30, batch_size=15, enable_backtesting=True)
        
        # Get stocks from CSV
        print("📁 Loading stocks from EQUITY_L.csv...")
        csv_fetcher = EquityCSVStocksFetcher()
        
        # Get consistent set of liquid stocks for comprehensive backtesting
        liquid_stocks = csv_fetcher.get_liquid_stocks(100)  # Reduced for thorough backtesting

        # Sort stocks alphabetically for consistent processing order
        liquid_stocks.sort()

        print(f"✅ Selected {len(liquid_stocks)} liquid stocks for comprehensive analysis")
        print(f"📋 Processing in consistent order: {', '.join(liquid_stocks[:10])}...")
        print(f"🔍 Each stock will be backtested with 30 days of historical data")
        
        # Run parallel analysis
        results = analyzer.analyze_stocks_parallel(liquid_stocks)
        
        if not results:
            print("❌ No results obtained from analysis")
            return
        
        # Get top 10 BUY stocks only
        top_10_buy = analyzer.get_top_buy_stocks(results, 10)

        if not top_10_buy:
            print("❌ No BUY signals found in current market conditions")
            return

        # Display results
        analyzer.display_top_buy_stocks(top_10_buy)

        print(f"\n📊 ANALYSIS SUMMARY:")
        print(f"   Total Stocks Analyzed: {len(results)}")
        print(f"   Stocks with BUY Signals: {len([s for s in results if 'BUY' in s['recommendation']])}")
        print(f"   Stocks with Positive Momentum: {len([s for s in results if s['change_percent'] > 0])}")
        print(f"   Success Rate: {(len(results)/len(liquid_stocks))*100:.1f}%")
        
        # Create reports directory if it doesn't exist
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
            print(f"📁 Created reports directory: {reports_dir}")

        # Save results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save top 10 BUY stocks to JSON
        top_10_file = os.path.join(reports_dir, f"top_10_buy_stocks_{timestamp}.json")
        with open(top_10_file, 'w', encoding='utf-8') as f:
            json.dump(top_10_buy, f, indent=2, ensure_ascii=False)

        # Save all results to JSON
        all_results_file = os.path.join(reports_dir, f"all_results_fast_analysis_{timestamp}.json")
        with open(all_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Generate summary report
        buy_stocks_count = len([s for s in results if 'BUY' in s['recommendation']])
        positive_momentum_count = len([s for s in results if s['change_percent'] > 0])

        report = f"""
🇮🇳 ULTRA-FAST PARALLEL BUY STOCKS ANALYSIS
==========================================
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 ANALYSIS SUMMARY:
===================
• Total Stocks Analyzed: {len(results)}
• Stocks with BUY Signals: {buy_stocks_count}
• Stocks with Positive Momentum: {positive_momentum_count}
• Success Rate: {(len(results)/len(liquid_stocks))*100:.1f}%
• Processing Speed: ~{len(results)/60:.1f} stocks/second

🏆 TOP 10 BUY RECOMMENDATIONS:
=============================
"""

        for i, stock in enumerate(top_10_buy, 1):
            combined_score = stock.get('combined_score', 0)
            report += f"{i:2d}. {stock['symbol']:<12} | ₹{stock['price']:>8.2f} | {stock['change_percent']:>+6.2f}% | Score: {stock['score']:>5.1f} | Combined: {combined_score:>6.1f} | {stock['recommendation']}\n"
        
        if top_10_buy:
            best_performer = top_10_buy[0]
            highest_gainer = max([s for s in results if s['change_percent'] > 0], key=lambda x: x['change_percent'], default={'symbol': 'N/A', 'change_percent': 0})
            highest_volume = max(results, key=lambda x: x['volume'])

            report += f"""
🎯 BUY TRADING INSIGHTS:
=======================
• Best BUY Performer: {best_performer['symbol']} (Combined Score: {best_performer.get('combined_score', 0):.1f})
• Highest Gainer: {highest_gainer['symbol']} ({highest_gainer['change_percent']:+.2f}%)
• Highest Volume: {highest_volume['symbol']} ({highest_volume['volume']:,})
• BUY Signal Rate: {(buy_stocks_count/len(results))*100:.1f}% of analyzed stocks

⚡ PERFORMANCE METRICS:
======================
• Parallel Workers: {analyzer.max_workers}
• Batch Size: {analyzer.batch_size}
• Processing Method: Multi-threaded batch processing
• Data Source: Yahoo Finance API + EQUITY_L.csv

🚀 NEXT STEPS:
=============
1. Focus on top 3-5 stocks for detailed analysis
2. Set up real-time alerts for price movements
3. Monitor volume and momentum during trading hours
4. Use proper risk management strategies

Generated by Ultra-Fast Parallel Stock Analyzer
⚡ Optimized for speed and accuracy
"""
        
        report_file = os.path.join(reports_dir, f"fast_analysis_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n💾 RESULTS SAVED TO REPORTS FOLDER:")
        print(f"   📁 Directory: {reports_dir}/")
        print(f"   🏆 Top 10: {os.path.basename(top_10_file)}")
        print(f"   📊 All Results: {os.path.basename(all_results_file)}")
        print(f"   📋 Report: {os.path.basename(report_file)}")
        print(f"   📂 Full paths:")
        print(f"      {top_10_file}")
        print(f"      {all_results_file}")
        print(f"      {report_file}")
        
        print(f"\n🎉 ULTRA-FAST ANALYSIS COMPLETED!")
        print(f"⚡ Ready for high-speed trading decisions!")

        return top_10_buy
        
    except Exception as e:
        print(f"❌ Error in fast parallel analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
