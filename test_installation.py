#!/usr/bin/env python3
"""
Test script to verify the installation and basic functionality
"""

import sys
import os
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import pandas as pd
        print("✓ pandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ numpy imported successfully")
    except ImportError as e:
        print(f"✗ numpy import failed: {e}")
        return False
    
    try:
        import yfinance as yf
        print("✓ yfinance imported successfully")
    except ImportError as e:
        print(f"✗ yfinance import failed: {e}")
        return False
    
    try:
        from tools.market_data import MarketData
        print("✓ MarketData tool imported successfully")
    except ImportError as e:
        print(f"✗ MarketData import failed: {e}")
        return False
    
    try:
        from tools.nlp_processor import NLPProcessor
        print("✓ NLPProcessor tool imported successfully")
    except ImportError as e:
        print(f"✗ NLPProcessor import failed: {e}")
        return False
    
    try:
        from config.config import config
        print("✓ Configuration imported successfully")
    except ImportError as e:
        print(f"✗ Configuration import failed: {e}")
        return False
    
    return True

def test_market_data():
    """Test market data functionality"""
    print("\nTesting market data functionality...")
    
    try:
        from tools.market_data import MarketData
        
        md = MarketData()
        
        # Test intraday data
        data = md.get_intraday_data('RELIANCE', interval='15m', period='1d')
        if not data.empty:
            print("✓ Intraday data retrieval working")
        else:
            print("⚠ Intraday data is empty (using mock data)")
        
        # Test indicators
        indicators = md.calculate_indicators(data)
        if 'RSI_14' in indicators.columns:
            print("✓ Technical indicators calculation working")
        else:
            print("✗ Technical indicators calculation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Market data test failed: {e}")
        return False

def test_nlp_processor():
    """Test NLP functionality"""
    print("\nTesting NLP functionality...")
    
    try:
        from tools.nlp_processor import NLPProcessor
        
        nlp = NLPProcessor()
        
        # Test sentiment analysis
        test_text = "RELIANCE shows strong growth and beats earnings estimates"
        result = nlp.analyze_sentiment(test_text)
        
        if 'sentiment' in result and 'stocks' in result:
            print("✓ Sentiment analysis working")
            print(f"  Detected sentiment: {result['sentiment']}")
            print(f"  Detected stocks: {result['stocks']}")
        else:
            print("✗ Sentiment analysis failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ NLP test failed: {e}")
        return False

def test_configuration():
    """Test configuration"""
    print("\nTesting configuration...")
    
    try:
        from config.config import config
        
        # Test basic config access
        symbols = config.default_symbols
        interval = config.default_interval
        
        print(f"✓ Configuration loaded successfully")
        print(f"  Default symbols: {symbols}")
        print(f"  Default interval: {interval}")
        print(f"  Mock data mode: {config.mock_data}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_strategy_agents():
    """Test strategy agents"""
    print("\nTesting strategy agents...")
    
    try:
        from agents.strategy_agents.trendline_breakout_agent import TrendlineBreakoutAgent
        from agents.strategy_agents.fisher_transform_agent import FisherTransformAgent
        
        # Test agent initialization
        trendline_agent = TrendlineBreakoutAgent()
        fisher_agent = FisherTransformAgent()
        
        print("✓ Strategy agents initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ Strategy agents test failed: {e}")
        return False

def test_backtrader_strategies():
    """Test Backtrader strategies"""
    print("\nTesting Backtrader strategies...")
    
    try:
        from strategies.backtrader_strategies import TrendlineBreakoutStrategy
        import backtrader as bt
        
        # Test strategy initialization
        cerebro = bt.Cerebro()
        cerebro.addstrategy(TrendlineBreakoutStrategy)
        
        print("✓ Backtrader strategies working")
        return True
        
    except ImportError as e:
        print(f"⚠ Backtrader not available: {e}")
        return True  # Not critical for basic functionality
    except Exception as e:
        print(f"✗ Backtrader strategies test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("INTRADAY TRADING ANALYZER - INSTALLATION TEST")
    print("="*60)
    print(f"Python version: {sys.version}")
    print(f"Test time: {datetime.now()}")
    print("="*60)
    
    tests = [
        ("Import Test", test_imports),
        ("Market Data Test", test_market_data),
        ("NLP Processor Test", test_nlp_processor),
        ("Configuration Test", test_configuration),
        ("Strategy Agents Test", test_strategy_agents),
        ("Backtrader Strategies Test", test_backtrader_strategies),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        print(f"Running {test_name}...")
        print(f"{'-'*40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print(f"{'='*60}")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return 0
    else:
        print("⚠ Some tests failed. Please check the installation.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
