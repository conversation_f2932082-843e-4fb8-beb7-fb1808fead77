#!/usr/bin/env python3
"""
Simplified version of the Intraday Trading Analyzer that works without CrewAI
This version demonstrates the core functionality using direct agent calls
"""

import os
from dotenv import load_dotenv
from tools.market_data import MarketData
from tools.nlp_processor import NLPProcessor
from tools.moneycontrol_scraper import MoneyControlScraper
from config.config import config
import pandas as pd
import numpy as np
from datetime import datetime

# Load environment variables
load_dotenv("config/api_keys.env")

class SimpleIntradayAnalyzer:
    def __init__(self):
        """Initialize the simplified analyzer"""
        self.market_data = MarketData()
        self.nlp_processor = NLPProcessor()
        self.news_scraper = MoneyControlScraper()
        
    def analyze_symbol(self, symbol: str) -> dict:
        """Analyze a single symbol using simplified strategy logic"""
        print(f"\n{'='*50}")
        print(f"ANALYZING: {symbol}")
        print(f"{'='*50}")
        
        try:
            # Get market data
            print("📊 Fetching market data...")
            data = self.market_data.get_intraday_data(symbol, interval='15m')
            
            if data.empty:
                print(f"❌ No data available for {symbol}")
                return None
            
            # Calculate indicators
            data_with_indicators = self.market_data.calculate_indicators(data)
            
            # Get news data
            print("📰 Fetching news data...")
            news_items = self.news_scraper.get_top_news()
            
            # Analyze news sentiment
            news_analysis = self.nlp_processor.analyze_sentiment(
                ' '.join([item['title'] for item in news_items[:5]])
            )
            
            # Apply simplified strategies
            strategies_results = self._apply_strategies(data_with_indicators, symbol)
            
            # Combine results
            final_result = self._combine_analysis(symbol, strategies_results, news_analysis)
            
            return final_result
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {e}")
            return None
    
    def _apply_strategies(self, data: pd.DataFrame, symbol: str) -> dict:
        """Apply simplified trading strategies"""
        results = {}
        
        # Get current values with error handling
        try:
            current_price = data['Close'].iloc[-1]
            volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].rolling(20).mean().iloc[-1]
            rsi = data.get('RSI_14', pd.Series([50] * len(data))).iloc[-1]
            ema_5 = data.get('5_EMA', data['Close']).iloc[-1]
            ema_20 = data.get('20_EMA', data['Close']).iloc[-1]

            # Convert to float for calculations
            current_price = float(current_price)
            volume = float(volume)
            avg_volume = float(avg_volume)
            rsi = float(rsi)
            ema_5 = float(ema_5)
            ema_20 = float(ema_20)
        except Exception as e:
            print(f"⚠️ Error getting indicator values: {e}")
            return {}
        
        print(f"💹 Current Price: {float(current_price):.2f}")
        print(f"📊 RSI(14): {float(rsi):.2f}")
        print(f"📈 EMA(5): {float(ema_5):.2f}, EMA(20): {float(ema_20):.2f}")
        print(f"📦 Volume Ratio: {float(volume/avg_volume):.2f}")
        
        # Strategy 1: Simple Trend Following
        trend_score = 0
        if ema_5 > ema_20 and current_price > ema_5:
            trend_score = 7
            trend_signal = 'BUY'
        elif ema_5 < ema_20 and current_price < ema_5:
            trend_score = 6
            trend_signal = 'SELL'
        else:
            trend_signal = 'HOLD'
        
        results['trend_following'] = {
            'score': trend_score,
            'signal': trend_signal,
            'entry': current_price,
            'stop_loss': ema_20,
            'target': current_price * 1.02 if trend_signal == 'BUY' else current_price * 0.98 if trend_signal == 'SELL' else None
        }
        
        # Strategy 2: RSI Mean Reversion
        rsi_score = 0
        if rsi < 30 and volume > 1.5 * avg_volume:
            rsi_score = 8
            rsi_signal = 'BUY'
        elif rsi > 70 and volume > 1.5 * avg_volume:
            rsi_score = 7
            rsi_signal = 'SELL'
        else:
            rsi_signal = 'HOLD'
        
        results['rsi_mean_reversion'] = {
            'score': rsi_score,
            'signal': rsi_signal,
            'entry': current_price,
            'stop_loss': current_price * 0.98 if rsi_signal == 'BUY' else current_price * 1.02 if rsi_signal == 'SELL' else None,
            'target': current_price * 1.03 if rsi_signal == 'BUY' else current_price * 0.97 if rsi_signal == 'SELL' else None
        }
        
        # Strategy 3: Volume Breakout
        volume_score = 0
        high_20 = float(data['High'].rolling(20).max().iloc[-1])
        low_20 = float(data['Low'].rolling(20).min().iloc[-1])

        if current_price > high_20 and volume > 2 * avg_volume:
            volume_score = 8
            volume_signal = 'BUY'
        elif current_price < low_20 and volume > 2 * avg_volume:
            volume_score = 7
            volume_signal = 'SELL'
        else:
            volume_signal = 'HOLD'
        
        results['volume_breakout'] = {
            'score': volume_score,
            'signal': volume_signal,
            'entry': current_price,
            'stop_loss': low_20 if volume_signal == 'BUY' else high_20 if volume_signal == 'SELL' else None,
            'target': current_price * 1.025 if volume_signal == 'BUY' else current_price * 0.975 if volume_signal == 'SELL' else None
        }
        
        return results
    
    def _combine_analysis(self, symbol: str, strategies: dict, news_analysis: dict) -> dict:
        """Combine strategy results into final recommendation"""
        
        # Calculate weighted scores
        valid_strategies = [(name, result) for name, result in strategies.items() if result['score'] > 0]
        
        if not valid_strategies:
            return {
                'symbol': symbol,
                'recommendation': 'HOLD',
                'confidence': 'Low',
                'score': 0,
                'strategies_used': [],
                'news_sentiment': news_analysis.get('sentiment', 'neutral')
            }
        
        # Calculate consensus
        buy_signals = [s for name, s in valid_strategies if s['signal'] == 'BUY']
        sell_signals = [s for name, s in valid_strategies if s['signal'] == 'SELL']
        
        if len(buy_signals) > len(sell_signals) and buy_signals:
            recommendation = 'BUY'
            avg_score = np.mean([s['score'] for s in buy_signals])
            entry = np.mean([s['entry'] for s in buy_signals])
            stop_loss = np.mean([s['stop_loss'] for s in buy_signals if s['stop_loss']])
            target = np.mean([s['target'] for s in buy_signals if s['target']])
        elif len(sell_signals) > len(buy_signals) and sell_signals:
            recommendation = 'SELL'
            avg_score = np.mean([s['score'] for s in sell_signals])
            entry = np.mean([s['entry'] for s in sell_signals])
            stop_loss = np.mean([s['stop_loss'] for s in sell_signals if s['stop_loss']])
            target = np.mean([s['target'] for s in sell_signals if s['target']])
        else:
            recommendation = 'HOLD'
            avg_score = np.mean([s['score'] for name, s in valid_strategies])
            entry = strategies[valid_strategies[0][0]]['entry']
            stop_loss = None
            target = None
        
        # Determine confidence
        if avg_score >= 8:
            confidence = 'High'
        elif avg_score >= 6:
            confidence = 'Medium'
        else:
            confidence = 'Low'
        
        # Adjust for news sentiment
        news_sentiment = news_analysis.get('sentiment', 'neutral')
        if (recommendation == 'BUY' and news_sentiment == 'negative') or \
           (recommendation == 'SELL' and news_sentiment == 'positive'):
            confidence = 'Low'
        
        return {
            'symbol': symbol,
            'recommendation': recommendation,
            'confidence': confidence,
            'score': round(avg_score, 2),
            'entry_price': round(entry, 2) if entry else None,
            'stop_loss': round(stop_loss, 2) if stop_loss else None,
            'target_price': round(target, 2) if target else None,
            'strategies_used': [name for name, _ in valid_strategies],
            'news_sentiment': news_sentiment,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_analysis(self, symbols: list) -> list:
        """Run analysis for multiple symbols"""
        print(f"\n🚀 Starting Intraday Analysis for {len(symbols)} symbols")
        print(f"⏰ Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = []
        for symbol in symbols:
            result = self.analyze_symbol(symbol)
            if result:
                results.append(result)
        
        # Sort by score (highest first)
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return results
    
    def print_results(self, results: list):
        """Print analysis results in a formatted way"""
        print(f"\n{'='*80}")
        print("📈 FINAL TRADING RECOMMENDATIONS")
        print(f"{'='*80}")
        
        if not results:
            print("❌ No valid recommendations generated")
            return
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['symbol']} - {result['recommendation']} ({result['confidence']} Confidence)")
            print(f"   Score: {result['score']}/10")
            if result.get('entry_price'):
                print(f"   Entry: ₹{result['entry_price']}")
            if result.get('target_price'):
                print(f"   Target: ₹{result['target_price']}")
            if result.get('stop_loss'):
                print(f"   Stop Loss: ₹{result['stop_loss']}")
            if result['strategies_used']:
                print(f"   Strategies: {', '.join(result['strategies_used'])}")
            print(f"   News Sentiment: {result['news_sentiment']}")


def main():
    """Main function"""
    # Default symbols to analyze
    symbols = config.default_symbols
    
    print("🏛️ SIMPLIFIED INTRADAY TRADING ANALYZER")
    print("=" * 50)
    print("This is a simplified version that works without CrewAI")
    print("It demonstrates the core functionality of the system")
    
    # Initialize analyzer
    analyzer = SimpleIntradayAnalyzer()
    
    # Run analysis
    results = analyzer.run_analysis(symbols)
    
    # Print results
    analyzer.print_results(results)
    
    print(f"\n✅ Analysis completed for {len(results)} symbols")
    print("💡 Tip: This is for educational purposes only. Always consult financial advisors!")


if __name__ == "__main__":
    main()
